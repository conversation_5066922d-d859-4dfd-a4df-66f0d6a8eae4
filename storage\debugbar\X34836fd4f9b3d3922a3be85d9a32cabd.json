{"__meta": {"id": "X34836fd4f9b3d3922a3be85d9a32cabd", "datetime": "2025-06-18 09:22:16", "utime": 1750209736.904057, "method": "GET", "uri": "/dahonglu", "ip": "127.0.0.1"}, "php": {"version": "8.0.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750209735.860297, "end": 1750209736.904072, "duration": 1.0437750816345215, "duration_str": "1.04s", "measures": [{"label": "Booting", "start": 1750209735.860297, "relative_start": 0, "end": 1750209736.492098, "relative_end": 1750209736.492098, "duration": 0.6318011283874512, "duration_str": "632ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1750209736.492115, "relative_start": 0.6318180561065674, "end": 1750209736.904083, "relative_end": 1.0967254638671875e-05, "duration": 0.4119679927825928, "duration_str": "412ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 43215264, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 5, "templates": [{"name": "yuanqu_test.index (resources/views/yuanqu_test/index.blade.php)", "param_count": 2, "params": ["sliders", "enterprises"], "type": "blade"}, {"name": "default.layouts.main (resources/views/default/layouts/main.blade.php)", "param_count": 24, "params": ["__env", "app", "errors", "sliders", "enterprises", "__currentLoopData", "slider", "loop", "association", "category", "notices", "notice", "forums", "enterprises_chunk", "ens", "enterprise", "index", "forum", "news", "new", "mentors", "mentors_chunk", "links", "link"], "type": "blade"}, {"name": "default.layouts.seo (resources/views/default/layouts/seo.blade.php)", "param_count": 0, "params": [], "type": "blade"}, {"name": "default.layouts.header (resources/views/default/layouts/header.blade.php)", "param_count": 24, "params": ["__env", "app", "errors", "sliders", "enterprises", "__currentLoopData", "slider", "loop", "association", "category", "notices", "notice", "forums", "enterprises_chunk", "ens", "enterprise", "index", "forum", "news", "new", "mentors", "mentors_chunk", "links", "link"], "type": "blade"}, {"name": "default.layouts.footer (resources/views/default/layouts/footer.blade.php)", "param_count": 24, "params": ["__env", "app", "errors", "sliders", "enterprises", "__currentLoopData", "slider", "loop", "association", "category", "notices", "notice", "forums", "enterprises_chunk", "ens", "enterprise", "index", "forum", "news", "new", "mentors", "mentors_chunk", "links", "link"], "type": "blade"}]}, "route": {"uri": "GET {association}", "middleware": "web, front_association", "controller": "App\\Http\\Controllers\\Home\\IndexController@index", "namespace": "App\\Http\\Controllers", "prefix": "/{association}", "where": [], "as": "front.login", "file": "<a href=\"phpstorm://open?file=/mnt/c/Users/<USER>/code/yuanqu/admin/app/Http/Controllers/Home/IndexController.php&line=102\">app/Http/Controllers/Home/IndexController.php:102-115</a>"}, "queries": {"nb_statements": 18, "nb_failed_statements": 0, "accumulated_duration": 0.01706, "accumulated_duration_str": "17.06ms", "statements": [{"sql": "select * from `associations` where `alias` = 'dahonglu' and `associations`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON>"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "middleware", "name": "front_association", "line": 68}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "line": 50}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "line": 78}], "duration": 0.00094, "duration_str": "940μs", "stmt_id": "middleware::front_association:68", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 0, "width_percent": 5.51}, {"sql": "select * from `sliders` where `show` = 1 and `association_id` = 2 and `sliders`.`deleted_at` is null order by `order` asc", "type": "query", "params": [], "bindings": ["1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "/app/Http/Controllers/Home/IndexController.php", "line": 104}, {"index": 15, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 48}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721}], "duration": 0.00088, "duration_str": "880μs", "stmt_id": "/app/Http/Controllers/Home/IndexController.php:104", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 5.51, "width_percent": 5.158}, {"sql": "select * from `enterprise` where `status` = 'normal' and exists (select * from `associations` inner join `association_enterprises` on `associations`.`id` = `association_enterprises`.`association_id` where `enterprise`.`id` = `association_enterprises`.`enterprise_id` and `status` = 'passed' and `associations`.`deleted_at` is null) and exists (select * from `associations` inner join `association_enterprises` on `associations`.`id` = `association_enterprises`.`association_id` where `enterprise`.`id` = `association_enterprises`.`enterprise_id` and `association_id` = 2 and `association_enterprises`.`status` = 'passed' and `associations`.`deleted_at` is null) and `enterprise`.`deleted_at` is null order by `recommend` desc, `order` asc, `id` desc limit 6", "type": "query", "params": [], "bindings": ["normal", "passed", "2", "passed"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "/app/Http/Controllers/Home/IndexController.php", "line": 112}, {"index": 15, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 48}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721}], "duration": 0.0019199999999999998, "duration_str": "1.92ms", "stmt_id": "/app/Http/Controllers/Home/IndexController.php:112", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 10.668, "width_percent": 11.254}, {"sql": "select * from `categories` where `alias` = 'tongzhigonggao' and `association_id` = 2 and `categories`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["tongzhigonggao", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "a4bbbf2dbd8c2db0a078f9d2d84dd8f6a10d6d1c", "line": 27}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 20, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.00138, "duration_str": "1.38ms", "stmt_id": "view::a4bbbf2dbd8c2db0a078f9d2d84dd8f6a10d6d1c:27", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 21.923, "width_percent": 8.089}, {"sql": "select `articles`.*, `article_categories`.`category_id` as `pivot_category_id`, `article_categories`.`article_id` as `pivot_article_id` from `articles` inner join `article_categories` on `articles`.`id` = `article_categories`.`article_id` where `article_categories`.`category_id` = 18 and `show` = 1 and `association_id` = 2 and `articles`.`deleted_at` is null order by `top` desc, `order` asc, `created_at` desc limit 4", "type": "query", "params": [], "bindings": ["18", "1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "a4bbbf2dbd8c2db0a078f9d2d84dd8f6a10d6d1c", "line": 29}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 19, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.00139, "duration_str": "1.39ms", "stmt_id": "view::a4bbbf2dbd8c2db0a078f9d2d84dd8f6a10d6d1c:29", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 30.012, "width_percent": 8.148}, {"sql": "select * from `forums` where `show` = 1 and `association_id` = 2 order by `top` desc, `order` asc, `created_at` desc limit 4", "type": "query", "params": [], "bindings": ["1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "a4bbbf2dbd8c2db0a078f9d2d84dd8f6a10d6d1c", "line": 62}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 19, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.001, "duration_str": "1ms", "stmt_id": "view::a4bbbf2dbd8c2db0a078f9d2d84dd8f6a10d6d1c:62", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 38.159, "width_percent": 5.862}, {"sql": "select * from `categories` where `alias` = 'x<PERSON><PERSON><PERSON>wen' and `association_id` = 2 and `categories`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "a4bbbf2dbd8c2db0a078f9d2d84dd8f6a10d6d1c", "line": 141}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 20, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.0008100000000000001, "duration_str": "810μs", "stmt_id": "view::a4bbbf2dbd8c2db0a078f9d2d84dd8f6a10d6d1c:141", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 44.021, "width_percent": 4.748}, {"sql": "select `articles`.*, `article_categories`.`category_id` as `pivot_category_id`, `article_categories`.`article_id` as `pivot_article_id` from `articles` inner join `article_categories` on `articles`.`id` = `article_categories`.`article_id` where `article_categories`.`category_id` = 17 and `show` = 1 and `association_id` = 2 and `articles`.`deleted_at` is null order by `top` desc, `order` asc, `created_at` desc limit 4", "type": "query", "params": [], "bindings": ["17", "1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "a4bbbf2dbd8c2db0a078f9d2d84dd8f6a10d6d1c", "line": 143}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 19, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.001, "duration_str": "1ms", "stmt_id": "view::a4bbbf2dbd8c2db0a078f9d2d84dd8f6a10d6d1c:143", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 48.769, "width_percent": 5.862}, {"sql": "select * from `mentors` where `show` = 1 and `association_id` = 2 order by `top` desc, `order` asc, `created_at` desc limit 5", "type": "query", "params": [], "bindings": ["1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "a4bbbf2dbd8c2db0a078f9d2d84dd8f6a10d6d1c", "line": 210}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 19, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.00083, "duration_str": "830μs", "stmt_id": "view::a4bbbf2dbd8c2db0a078f9d2d84dd8f6a10d6d1c:210", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 54.631, "width_percent": 4.865}, {"sql": "select * from `links` where `show` = 1 and `association_id` = 2 and `links`.`deleted_at` is null order by `order` desc, `created_at` desc", "type": "query", "params": [], "bindings": ["1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "a4bbbf2dbd8c2db0a078f9d2d84dd8f6a10d6d1c", "line": 250}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 19, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.00086, "duration_str": "860μs", "stmt_id": "view::a4bbbf2dbd8c2db0a078f9d2d84dd8f6a10d6d1c:250", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 59.496, "width_percent": 5.041}, {"sql": "select * from `navs` where (`parent_id` = 0 or `parent_id` is null) and `show` = 1 and `association_id` = 2 and `navs`.`deleted_at` is null order by `order` asc", "type": "query", "params": [], "bindings": ["0", "1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "4b551dc7569e49db71ac2552133a3f418724512c", "line": 25}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 19, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.0009, "duration_str": "900μs", "stmt_id": "view::4b551dc7569e49db71ac2552133a3f418724512c:25", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 64.537, "width_percent": 5.275}, {"sql": "select * from `navs` where `parent_id` = 47 and `show` = 1 and `association_id` = 2 and `navs`.`deleted_at` is null order by `order` asc", "type": "query", "params": [], "bindings": ["47", "1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "4b551dc7569e49db71ac2552133a3f418724512c", "line": 31}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 19, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.00062, "duration_str": "620μs", "stmt_id": "view::4b551dc7569e49db71ac2552133a3f418724512c:31", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 69.812, "width_percent": 3.634}, {"sql": "select * from `navs` where `parent_id` = 14 and `show` = 1 and `association_id` = 2 and `navs`.`deleted_at` is null order by `order` asc", "type": "query", "params": [], "bindings": ["14", "1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "4b551dc7569e49db71ac2552133a3f418724512c", "line": 31}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 19, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.00085, "duration_str": "850μs", "stmt_id": "view::4b551dc7569e49db71ac2552133a3f418724512c:31", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 73.447, "width_percent": 4.982}, {"sql": "select * from `navs` where `parent_id` = 12 and `show` = 1 and `association_id` = 2 and `navs`.`deleted_at` is null order by `order` asc", "type": "query", "params": [], "bindings": ["12", "1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "4b551dc7569e49db71ac2552133a3f418724512c", "line": 31}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 19, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.00063, "duration_str": "630μs", "stmt_id": "view::4b551dc7569e49db71ac2552133a3f418724512c:31", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 78.429, "width_percent": 3.693}, {"sql": "select * from `navs` where `parent_id` = 13 and `show` = 1 and `association_id` = 2 and `navs`.`deleted_at` is null order by `order` asc", "type": "query", "params": [], "bindings": ["13", "1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "4b551dc7569e49db71ac2552133a3f418724512c", "line": 31}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 19, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.00067, "duration_str": "670μs", "stmt_id": "view::4b551dc7569e49db71ac2552133a3f418724512c:31", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 82.122, "width_percent": 3.927}, {"sql": "select * from `navs` where `parent_id` = 45 and `show` = 1 and `association_id` = 2 and `navs`.`deleted_at` is null order by `order` asc", "type": "query", "params": [], "bindings": ["45", "1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "4b551dc7569e49db71ac2552133a3f418724512c", "line": 31}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 19, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.00057, "duration_str": "570μs", "stmt_id": "view::4b551dc7569e49db71ac2552133a3f418724512c:31", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 86.049, "width_percent": 3.341}, {"sql": "select * from `navs` where `parent_id` = 46 and `show` = 1 and `association_id` = 2 and `navs`.`deleted_at` is null order by `order` asc", "type": "query", "params": [], "bindings": ["46", "1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "4b551dc7569e49db71ac2552133a3f418724512c", "line": 31}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 19, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.00093, "duration_str": "930μs", "stmt_id": "view::4b551dc7569e49db71ac2552133a3f418724512c:31", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 89.39, "width_percent": 5.451}, {"sql": "select * from `pages` where `alias` = '园区简介' limit 1", "type": "query", "params": [], "bindings": ["园区简介"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "1ab61f277331ad0356d6d880ff0067a8dad67ade", "line": 32}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 20, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.00088, "duration_str": "880μs", "stmt_id": "view::1ab61f277331ad0356d6d880ff0067a8dad67ade:32", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 94.842, "width_percent": 5.158}]}, "models": {"data": {"App\\Models\\Page": 1, "App\\Models\\Nav": 9, "App\\Models\\Link": 1, "App\\Models\\Forum": 4, "App\\Models\\Article": 7, "App\\Models\\Category": 2, "App\\Models\\Enterprise": 6, "App\\Models\\Slider": 1, "App\\Models\\Association": 1}, "count": 32}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "TDUMv4PtOg9drnklts1GloXvJkRpSg7OqATAFfaR", "_previous": "array:1 [\n  \"url\" => \"http://yuanqu.test/dahonglu\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/dahonglu", "status_code": "<pre class=sf-dump id=sf-dump-1673326624 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1673326624\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1855997906 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1855997906\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1629586164 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1629586164\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-821580334 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"706 characters\">XSRF-TOKEN=eyJpdiI6IlB3ZTQ0dGtCTFBZcmN5b1ozNFM4bVE9PSIsInZhbHVlIjoiMUMxS2U2Y252dXJ6amV0U1c1TngxOW1CanBWYmR2VVY3cm4vM09pYndlUHFiR05ZSVZZUTdKT3EvVmtxYzI2V2Vyb1pQVHhOZXMzd09rekxlOFM0b3lJU2dFZitVVW5pQnY5LzA5SWlvQ1kwSHV3dWprdFdUS2hQL1BPQkwrZDMiLCJtYWMiOiI4MjJjMWM3MDZjYTk3ZGQ2YjllYjEwNmU5YjFlMWRiOWI4NjFhMjE2MWM1YTNjZTNjMjJhYTU0ZWVhMDkyZTYyIiwidGFnIjoiIn0%3D; _session=eyJpdiI6ImxvUVpHc0g3LzAveWtmeU1QV0h6ZGc9PSIsInZhbHVlIjoicWhYaDhkTlJCU0JRZkNyZS9pazAwMmc5aTk2QTdTMmZhSWJDNUkrZEFVazB1clNvUm0xMVB1TEIxYjRaZUlmYUxSaktaTTFuMlBWMHpVQ0NhSGIzTDVsdk9sYlNXRlZHeWU0dkV6VGtiQVRVSzE3TWNsQTIwN1MyQVB1blZBY0UiLCJtYWMiOiIyZmVjNDM5MzY0OTU4NDY0NzljNjg3MjIxZDNmZTg4NzU1ZDc0ZGFiNmYyYzQ5MjcwNjgxNDUzMGU4MDQ0ZWIwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">zh-CN,zh-TW;q=0.9,zh;q=0.8,en-US;q=0.7,en;q=0.6,ja;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">http://yuanqu.test/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">yuanqu.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-821580334\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-263147246 data-indent-pad=\"  \"><span class=sf-dump-note>array:37</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>USER</span>\" => \"<span class=sf-dump-str title=\"3 characters\">www</span>\"\n  \"<span class=sf-dump-key>HOME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">/home/<USER>/span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"706 characters\">XSRF-TOKEN=eyJpdiI6IlB3ZTQ0dGtCTFBZcmN5b1ozNFM4bVE9PSIsInZhbHVlIjoiMUMxS2U2Y252dXJ6amV0U1c1TngxOW1CanBWYmR2VVY3cm4vM09pYndlUHFiR05ZSVZZUTdKT3EvVmtxYzI2V2Vyb1pQVHhOZXMzd09rekxlOFM0b3lJU2dFZitVVW5pQnY5LzA5SWlvQ1kwSHV3dWprdFdUS2hQL1BPQkwrZDMiLCJtYWMiOiI4MjJjMWM3MDZjYTk3ZGQ2YjllYjEwNmU5YjFlMWRiOWI4NjFhMjE2MWM1YTNjZTNjMjJhYTU0ZWVhMDkyZTYyIiwidGFnIjoiIn0%3D; _session=eyJpdiI6ImxvUVpHc0g3LzAveWtmeU1QV0h6ZGc9PSIsInZhbHVlIjoicWhYaDhkTlJCU0JRZkNyZS9pazAwMmc5aTk2QTdTMmZhSWJDNUkrZEFVazB1clNvUm0xMVB1TEIxYjRaZUlmYUxSaktaTTFuMlBWMHpVQ0NhSGIzTDVsdk9sYlNXRlZHeWU0dkV6VGtiQVRVSzE3TWNsQTIwN1MyQVB1blZBY0UiLCJtYWMiOiIyZmVjNDM5MzY0OTU4NDY0NzljNjg3MjIxZDNmZTg4NzU1ZDc0ZGFiNmYyYzQ5MjcwNjgxNDUzMGU4MDQ0ZWIwIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"56 characters\">zh-CN,zh-TW;q=0.9,zh;q=0.8,en-US;q=0.7,en;q=0.6,ja;q=0.5</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"19 characters\">http://yuanqu.test/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  \"<span class=sf-dump-key>HTTP_PRAGMA</span>\" => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"11 characters\">yuanqu.test</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"\"\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"11 characters\">yuanqu.test</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">58501</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"12 characters\">nginx/1.20.1</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"43 characters\">/mnt/c/Users/<USER>/code/yuanqu/admin/public</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_URI</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"9 characters\">/dahonglu</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"53 characters\">/mnt/c/Users/<USER>/code/yuanqu/admin/public/index.php</span>\"\n  \"<span class=sf-dump-key>FCGI_ROLE</span>\" => \"<span class=sf-dump-str title=\"9 characters\">RESPONDER</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1750209735.8603</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1750209735</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-263147246\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-689899390 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TDUMv4PtOg9drnklts1GloXvJkRpSg7OqATAFfaR</span>\"\n  \"<span class=sf-dump-key>_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dOiJy0ZAZXxtiQ97LaRovROa48WaGw1obS8eq4I8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-689899390\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1282520559 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 18 Jun 2025 01:22:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlI0MEtnSDhwa0dIRkVINmR4cjhvQUE9PSIsInZhbHVlIjoia0REOG9yYlZCdGRtS0ZSY3JGYVdFYnN4WWI4RGNpWDFXejhIQ2FNRlFtUUN6UXZUY2VwUnN2TTFtdlZWTDNIbTdQOHJtSmxGTWo1ZmZlVEhuL096bEtzMHN5TlBNek9ta0JDcW8yUXVDQ0ZVQSs0bTZXeGFYcTFURnFmYlpzSWUiLCJtYWMiOiJjM2I3YTllZTUyMzA3MjFiMDQwNGFjYjUxMTc5YTkwNmJhYTQ2NWYyMzE1NmFkNTA0ZDgwZTFkY2RhMWE4MGFhIiwidGFnIjoiIn0%3D; expires=Wed, 18-Jun-2025 03:22:16 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"436 characters\">_session=eyJpdiI6IklpeisrMVZ1Tmg1YWQ3MkUzZ29ERFE9PSIsInZhbHVlIjoiWnlvN2pPRWx3K1NHay94Tk1pTXdMV0RSZFFmY3pnaDJ1bklCSzRlR01xS0xFZWlPejhvRkt1OUduTG1ralpzVUF3MjVBWG9YS3ArVDJWY2xZaDl5NWNwZ0l1cHphRjZsMDk3MlVCZGdDNXVyRnhBMnh0dWVXZy9SbFA2T1llVTYiLCJtYWMiOiJhYmY5ODU1NjljMzY0ZGYxYzM2ZmRjNWZkMWY5ZDM4YWEwMWU4OGQ5MjAzNmQ2ZDNmYTc4Mjk0ODU2MGQzMjg2IiwidGFnIjoiIn0%3D; expires=Wed, 18-Jun-2025 03:22:16 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlI0MEtnSDhwa0dIRkVINmR4cjhvQUE9PSIsInZhbHVlIjoia0REOG9yYlZCdGRtS0ZSY3JGYVdFYnN4WWI4RGNpWDFXejhIQ2FNRlFtUUN6UXZUY2VwUnN2TTFtdlZWTDNIbTdQOHJtSmxGTWo1ZmZlVEhuL096bEtzMHN5TlBNek9ta0JDcW8yUXVDQ0ZVQSs0bTZXeGFYcTFURnFmYlpzSWUiLCJtYWMiOiJjM2I3YTllZTUyMzA3MjFiMDQwNGFjYjUxMTc5YTkwNmJhYTQ2NWYyMzE1NmFkNTA0ZDgwZTFkY2RhMWE4MGFhIiwidGFnIjoiIn0%3D; expires=Wed, 18-Jun-2025 03:22:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"408 characters\">_session=eyJpdiI6IklpeisrMVZ1Tmg1YWQ3MkUzZ29ERFE9PSIsInZhbHVlIjoiWnlvN2pPRWx3K1NHay94Tk1pTXdMV0RSZFFmY3pnaDJ1bklCSzRlR01xS0xFZWlPejhvRkt1OUduTG1ralpzVUF3MjVBWG9YS3ArVDJWY2xZaDl5NWNwZ0l1cHphRjZsMDk3MlVCZGdDNXVyRnhBMnh0dWVXZy9SbFA2T1llVTYiLCJtYWMiOiJhYmY5ODU1NjljMzY0ZGYxYzM2ZmRjNWZkMWY5ZDM4YWEwMWU4OGQ5MjAzNmQ2ZDNmYTc4Mjk0ODU2MGQzMjg2IiwidGFnIjoiIn0%3D; expires=Wed, 18-Jun-2025 03:22:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1282520559\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-944712462 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TDUMv4PtOg9drnklts1GloXvJkRpSg7OqATAFfaR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://yuanqu.test/dahonglu</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-944712462\", {\"maxDepth\":0})</script>\n"}}