<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>科技园</title>
    <script type="text/javascript" src="js/unocss.runtime.js"></script>
    <link rel="stylesheet" href="css/common.css" />
    <link rel="stylesheet" href="css/index.css" />

    <script src="js/swiper-bundle.min.js"></script>
    <link rel="stylesheet" href="css/swiper-bundle.min.css" />
  </head>
  <body>
    <header class="header">
      <div class="banner">
        <img src="images/banner.png" alt="banner" />
      </div>

      <div class="container mx-auto px-4">
        <!-- logo、搜索框 -->
        <div
          class="logo flex flex-col lg:flex-row justify-between gap-2"
          frag="面板02"
        >
          <a href="/" class="flex flex-col lg:flex-row items-center gap-4">
            <img src="images/logo.png" alt="logo" />
          </a>
          <div class="search">
            <input type="text" placeholder="搜索" />
            <button>搜索</button>
          </div>
        </div>
        <!-- 导航栏 -->
        <nav frag="面板03">
          <!--[NaviStructBegin]-->
          <ul
            class="nav-list flex flex-wrap"
            frag="窗口31"
            portletmode="simpleSudyNavi"
          >
            <!--[NaviItemCycleBegin]-->
            <li class="flex-shrink-0 {级别样式} {选中样式}">
              <a href="list.html">首页</a>
            </li>
            <li class="flex-shrink-0 {级别样式} {选中样式}">
              <a href="list.html">园区介绍</a>
              <!--[MenuStructBegin]-->
              <ul class="sub-menu">
                <!--[MenuItemCycleBegin]-->
                <li class="sub-item {级别样式}">
                  <a class="sub-link" href="list.html" target="{打开方式}"
                    >通知公告</a
                  ><!--[SubMenuList]-->
                </li>
                <!--[MenuItemCycleEnd]-->
              </ul>
              <!--[MenuStructEnd]-->
            </li>
            <li class="flex-shrink-0 {级别样式} {选中样式}">
              <a href="list.html">通知公告</a>
            </li>
            <li class="flex-shrink-0 {级别样式} {选中样式}">
              <a href="list.html">园区动态</a>
            </li>
            <li class="flex-shrink-0 {级别样式} {选中样式}">
              <a href="list.html">园区服务</a>
            </li>
            <li class="flex-shrink-0 {级别样式} {选中样式}">
              <a href="list.html">园区企业</a>
            </li>
            <li class="flex-shrink-0 {级别样式} {选中样式}">
              <a href="list.html">技术转移</a>
            </li>
            <li class="flex-shrink-0 {级别样式} {选中样式}">
              <a href="list.html">办事中心</a>
            </li>
            <li class="flex-shrink-0 {级别样式} {选中样式}">
              <a href="list.html">联系我们</a>
            </li>
          </ul>
          <!--[NaviStructEnd]-->
        </nav>
      </div>
    </header>

    <div class="content">
      <section class="section section-1">
        <div class="container grid grid-cols-1 lg:grid-cols-3 gap-8 mx-auto px-4">
          <div class="swiper">
            <div
              class="swiper-container"
              frag="窗口41"
              portletmode="simpleNews"
            >
              <div class="w-full h-full" frag="窗口内容">
                <!--[InfoCycleBegin]-->
                <div class="swiper-slide">
                  <a href="detail.html" target="_blank">
                    <img
                      src="images/cover.png"
                      alt="约图线程派置并极展圆石场专海战交决转或划年单道记华群约图线程派置并极展圆石场专海战交决转或划年单道记华群"
                    />
                    <div class="swiper-slide-content">
                      约图线程派置并极展圆石场专海战交决转或划年单道记华群约图线程派置并极展圆石场专海战交决转或划年单道记华群
                    </div>
                    <div class="date">2023-06-07</div>
                  </a>
                </div>
                <!--[InfoCycleEnd]-->
              </div>
              <div class="swiper-pagination">
                <span class="swiper-pagination-bullet"></span>
                <span class="swiper-pagination-bullet"></span>
                <span class="swiper-pagination-bullet"></span>
              </div>
            </div>
          </div>

          <div
            class="data-box flex flex-col"
            frag="窗口42"
            portletmode="simpleNews"
          >
            <div
              class="title-wrap flex flex-shrink-0 items-center justify-between gap-2"
            >
              <div class="title flex items-center gap-4">
                <h2>园区动态</h2>
                <span class="sub-title">News</span>
              </div>
              <div class="more flex-shrink-0" frag="按钮" type="更多">
                <img src="images/more.svg" frag="按钮内容" alt="更多" />
              </div>
            </div>
            <div frag="窗口内容">
              <div class="flex-1">
                <ul class="data-list">
                  <!--[InfoCycleBegin]-->
                  <li class="data-item i{序号值}">
                    <span class="data-title">
                      约图线程派置并极展圆石场专海战交决转或划年单道记华群约图线程派置并极展圆石场专海战交决转或划年单道记华群
                    </span>
                    <span class="date"> 2023-06-07 </span>
                  </li>
                  <!--[InfoCycleEnd]-->
                </ul>
              </div>
            </div>
          </div>
          <div
            class="data-box flex flex-col"
            frag="窗口42"
            portletmode="simpleNews"
          >
            <div
              class="title-wrap flex flex-shrink-0 items-center justify-between gap-2"
            >
              <div class="title flex items-center gap-4">
                <h2>通知公告</h2>
                <span class="sub-title">Notice</span>
              </div>
              <div class="more flex-shrink-0" frag="按钮" type="更多">
                <img src="images/more.svg" frag="按钮内容" alt="更多" />
              </div>
            </div>
            <div frag="窗口内容">
              <div class="flex-1">
                <ul class="data-list">
                  <!--[InfoCycleBegin]-->
                  <li class="data-item i{序号值}">
                    <span class="data-title">
                      约图线程派置并极展圆石场专海战交决转或划年单道记华群约图线程派置并极展圆石场专海战交决转或划年单道记华群
                    </span>
                    <span class="date"> 2023-06-07 </span>
                  </li>
                  <!--[InfoCycleEnd]-->
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      <div class="section section-2">
        <div class="container grid grid-cols-1 lg:grid-cols-2 gap-8 mx-auto px-4">
          <div class="w-full flex flex-wrap">
            <div class="label w-full md:w-1/4">
              <div class="block-area">
                <h3 class="block-title">园区企业</h3>
                <h4 class="block-subtitle">Enterprises</h4>
                <a
                  class="block-more-btn"
                  href="http://127.0.0.1:8000/kejiyuan/enterprises"
                  >查看全部</a
                >
              </div>
              <div
                class="enterprise-swiper-pagination swiper-pagination-clickable swiper-pagination-bullets swiper-pagination-horizontal swiper-pagination-lock"
              >
                <span
                  class="swiper-pagination-bullet swiper-pagination-bullet-active"
                  tabindex="0"
                  role="button"
                  aria-label="Go to slide 1"
                  aria-current="true"
                ></span>
              </div>
            </div>
            <div
              class="enterprise-content-wrapper enterprise-content-swiper-container w-full md:w-3/4 swiper-initialized swiper-horizontal swiper-pointer-events swiper-backface-hidden"
            >
              <div
                class="enterprise-list-wrapper swiper-wrapper"
                id="swiper-wrapper-51779bf9cbe9bb8e"
                aria-live="polite"
                style="transform: translate3d(0px, 0px, 0px)"
              >
                <div
                  class="enterprise-list swiper-slide swiper-slide-active"
                  style="grid-template-columns: repeat(2, 1fr); width: 493px"
                  role="group"
                  aria-label="1 / 1"
                >
                  <a
                    class="enterprise-item"
                    href="http://127.0.0.1:8000/kejiyuan/e/18"
                  >
                    <img
                      class="logo"
                      src="https://zhyq.sdut.edu.cn/storage/client/NQ/2023/7/8/rZZbmNY30Hi3pJ4Jabk61kAT1UleKMueQ3rn18jE.jpg"
                      alt=""
                    />
                    <div class="info">
                      <h4 class="name">海之纳(山东)文化科技发展有限公司</h4>
                      <div class="direction">居民服务、修理和其他服务业</div>
                    </div>
                  </a>
                  <a
                    class="enterprise-item"
                    href="http://127.0.0.1:8000/kejiyuan/e/16"
                  >
                    <img
                      class="logo"
                      src="https://zhyq.sdut.edu.cn/storage/client/NQ/2023/7/8/w1wrWDkC4UNLYcWMlDFQYqqwimDcjIP28Scaqc2a.jpg"
                      alt=""
                    />
                    <div class="info">
                      <h4 class="name">山东萌芽网络科技有限公司</h4>
                      <div class="direction">
                        信息传输、软件和信息技术服务业
                      </div>
                    </div>
                  </a>
                  <a
                    class="enterprise-item"
                    href="http://127.0.0.1:8000/kejiyuan/e/15"
                  >
                    <img
                      class="logo"
                      src="https://zhyq.sdut.edu.cn/storage/client/NQ/2023/7/8/CoQKL1Tydh2jz0e6dUeof0ecM4Gr5P64LqqUXHaC.jpg"
                      alt=""
                    />
                    <div class="info">
                      <h4 class="name">悦媒(淄博)信息科技有限公司</h4>
                      <div class="direction">
                        信息传输、软件和信息技术服务业
                      </div>
                    </div>
                  </a>
                </div>
              </div>
              <span
                class="swiper-notification"
                aria-live="assertive"
                aria-atomic="true"
              ></span>
            </div>
          </div>
          <div
            class="data-box flex flex-col"
            frag="窗口42"
            portletmode="simpleNews"
          >
            <div
              class="title-wrap flex flex-shrink-0 items-center justify-between gap-2"
            >
              <div class="title flex items-center gap-4">
                <h2>科研成果</h2>
                <span class="sub-title">Achievement</span>
              </div>
              <div class="more flex-shrink-0" frag="按钮" type="更多">
                <img src="images/more.svg" frag="按钮内容" alt="更多" />
              </div>
            </div>
            <div frag="窗口内容">
              <div class="flex-1">
                <ul class="data-list">
                  <!--[InfoCycleBegin]-->
                  <li class="data-item i{序号值}">
                    <span class="data-title">
                      约图线程派置并极展圆石场专海战交决转或划年单道记华群约图线程派置并极展圆石场专海战交决转或划年单道记华群
                    </span>
                    <span class="date"> 2023-06-07 </span>
                  </li>
                  <!--[InfoCycleEnd]-->
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="link">
      <div class="container">
        <h2>友情链接：</h2>
        <ul>
          <li><a href="#">市科技局</a></li>
          <li><a href="#">山东省教育厅</a></li>
          <li><a href="#">山东省科技厅</a></li>
          <li><a href="#">国家自然科学基金委员会</a></li>
          <li><a href="#">科学技术部</a></li>
          <li><a href="#">国家科技管理信息系统公共服务平台</a></li>
          <li><a href="#">山东省科技云平台</a></li>
          <li><a href="#">淄博市发展和改革委员会</a></li>
          <li><a href="#">山东省发展和改革委员会</a></li>
        </ul>
      </div>
    </div>

    <footer class="footer">
      <div class="container">
        <div class="footer-top">
          <div class="footer-top-left">
            <img src="images/logo.png" alt="logo" />
          </div>
          <div class="footer-top-right">
            <ul>
              <li>
                <span class="footer-top-right-title">联系电话：</span>
                <span class="footer-top-right-content">0533-2781918</span>
              </li>

              <li>
                <span class="footer-top-right-title">邮箱：</span>
                <span class="footer-top-right-content"
                  ><EMAIL></span
                >
              </li>

              <li>
                <span class="footer-top-right-title">地址：</span>
                <span class="footer-top-right-content"
                  >山东省淄博市张店区人民路188号</span
                >
              </li>
            </ul>
          </div>
        </div>

        <div class="copyright">
          <p>
            Copyright © 2025 科技园. All Rights Reserved.
            &nbsp;&nbsp;&nbsp;&nbsp;技术支持：萌芽科技
          </p>
        </div>
      </div>
    </footer>

    <script src="js/index.js"></script>
  </body>
</html>
