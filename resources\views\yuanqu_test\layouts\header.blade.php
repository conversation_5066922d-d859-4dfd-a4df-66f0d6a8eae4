<header class="header">
    @if(isset($show_banner) && $show_banner)
    <div class="banner">
        <img src="{{ asset('static/yuanqu_test/images/banner.png') }}" alt="banner" />
    </div>
    @endif

    <div class="container mx-auto px-4">
        <!-- logo、搜索框 -->
        <div class="logo flex flex-col lg:flex-row justify-between gap-2">
            <a href="{{ url('/') }}" class="flex flex-col lg:flex-row items-center gap-4">
                <img src="{{ asset('static/yuanqu_test/images/' . (isset($logo_type) && $logo_type == 'white' ? 'logo2.png' : 'logo.png')) }}" alt="logo" />
            </a>
            <div class="search">
                <form action="{{ url('/search') }}" method="GET">
                    <input type="text" name="q" placeholder="搜索" value="{{ request('q') }}" />
                    <button type="submit">搜索</button>
                </form>
            </div>
        </div>
        
        <!-- 导航栏 -->
        <nav>
            <ul class="nav-list flex flex-wrap">
                <li class="flex-shrink-0 {{ request()->is('/') ? 'active' : '' }}">
                    <a href="{{ url('/') }}">首页</a>
                </li>
                <li class="flex-shrink-0 {{ request()->is('about*') ? 'active' : '' }}">
                    <a href="{{ url('/about') }}">园区介绍</a>
                    <ul class="sub-menu">
                        <li class="sub-item">
                            <a class="sub-link" href="{{ url('/about/overview') }}">园区概况</a>
                        </li>
                        <li class="sub-item">
                            <a class="sub-link" href="{{ url('/about/history') }}">发展历程</a>
                        </li>
                    </ul>
                </li>
                <li class="flex-shrink-0 {{ request()->is('notice*') ? 'active' : '' }}">
                    <a href="{{ url('/notice') }}">通知公告</a>
                </li>
                <li class="flex-shrink-0 {{ request()->is('news*') ? 'active' : '' }}">
                    <a href="{{ url('/news') }}">园区动态</a>
                </li>
                <li class="flex-shrink-0 {{ request()->is('service*') ? 'active' : '' }}">
                    <a href="{{ url('/service') }}">园区服务</a>
                </li>
                <li class="flex-shrink-0 {{ request()->is('enterprises*') ? 'active' : '' }}">
                    <a href="{{ url('/enterprises') }}">园区企业</a>
                </li>
                <li class="flex-shrink-0 {{ request()->is('technology*') ? 'active' : '' }}">
                    <a href="{{ url('/technology') }}">技术转移</a>
                </li>
                <li class="flex-shrink-0 {{ request()->is('office*') ? 'active' : '' }}">
                    <a href="{{ url('/office') }}">办事中心</a>
                </li>
                <li class="flex-shrink-0 {{ request()->is('contact*') ? 'active' : '' }}">
                    <a href="{{ url('/contact') }}">联系我们</a>
                </li>
            </ul>
        </nav>
    </div>
</header>
