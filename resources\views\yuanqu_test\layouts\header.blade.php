<header class="header">
    @if(isset($show_banner) && $show_banner)
    <div class="banner">
        <img src="{{ asset('static/yuanqu_test/images/banner.png') }}" alt="banner" />
    </div>
    @endif

    <div class="container mx-auto px-4">
        <!-- logo、搜索框 -->
        <div class="logo flex flex-col lg:flex-row justify-between gap-2">
            <a href="{{ url('/kejiyuan') }}" class="flex flex-col lg:flex-row items-center gap-4">
                <img src="{{ asset('static/yuanqu_test/images/' . (isset($logo_type) && $logo_type == 'white' ? 'logo2.png' : 'logo.png')) }}" alt="logo" />
            </a>
            <div class="search">
                <form action="{{ url('/kejiyuan/search') }}" method="GET">
                    <input type="text" name="q" placeholder="搜索" value="{{ request('q') }}" />
                    <button type="submit">搜索</button>
                </form>
            </div>
        </div>
        
        <!-- 导航栏 -->
        <nav>
            <ul class="nav-list flex flex-wrap">
                <li class="flex-shrink-0 {{ request()->is('*/') || request()->is('/') ? 'active' : '' }}">
                    <a href="{{ url('/kejiyuan') }}">首页</a>
                </li>
                <li class="flex-shrink-0 {{ request()->is('*/about*') ? 'active' : '' }}">
                    <a href="{{ url('/kejiyuan/about') }}">园区介绍</a>
                    <ul class="sub-menu">
                        <li class="sub-item">
                            <a class="sub-link" href="{{ url('/kejiyuan/about/overview') }}">园区概况</a>
                        </li>
                        <li class="sub-item">
                            <a class="sub-link" href="{{ url('/kejiyuan/about/history') }}">发展历程</a>
                        </li>
                    </ul>
                </li>
                <li class="flex-shrink-0 {{ request()->is('*/notice*') ? 'active' : '' }}">
                    <a href="{{ url('/kejiyuan/notice') }}">通知公告</a>
                </li>
                <li class="flex-shrink-0 {{ request()->is('*/news*') ? 'active' : '' }}">
                    <a href="{{ url('/kejiyuan/news') }}">园区动态</a>
                </li>
                <li class="flex-shrink-0 {{ request()->is('*/service*') ? 'active' : '' }}">
                    <a href="{{ url('/kejiyuan/service') }}">园区服务</a>
                </li>
                <li class="flex-shrink-0 {{ request()->is('*/enterprises*') ? 'active' : '' }}">
                    <a href="{{ url('/kejiyuan/enterprises') }}">园区企业</a>
                </li>
                <li class="flex-shrink-0 {{ request()->is('*/technology*') ? 'active' : '' }}">
                    <a href="{{ url('/kejiyuan/technology') }}">技术转移</a>
                </li>
                <li class="flex-shrink-0 {{ request()->is('*/office*') ? 'active' : '' }}">
                    <a href="{{ url('/kejiyuan/office') }}">办事中心</a>
                </li>
                <li class="flex-shrink-0 {{ request()->is('*/contact*') ? 'active' : '' }}">
                    <a href="{{ url('/kejiyuan/contact') }}">联系我们</a>
                </li>
            </ul>
        </nav>
    </div>
</header>
