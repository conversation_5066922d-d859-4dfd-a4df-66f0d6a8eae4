{"__meta": {"id": "X74357053e39be7b745ab35ca7607e1ca", "datetime": "2025-06-18 09:45:29", "utime": 1750211129.635061, "method": "GET", "uri": "/kejiyuan/", "ip": "127.0.0.1"}, "php": {"version": "8.0.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750211128.561545, "end": 1750211129.635077, "duration": 1.0735321044921875, "duration_str": "1.07s", "measures": [{"label": "Booting", "start": 1750211128.561545, "relative_start": 0, "end": 1750211129.264404, "relative_end": 1750211129.264404, "duration": 0.7028591632843018, "duration_str": "703ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1750211129.264416, "relative_start": 0.7028710842132568, "end": 1750211129.635079, "relative_end": 1.9073486328125e-06, "duration": 0.3706629276275635, "duration_str": "371ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 40271608, "peak_usage_str": "38MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "yuanqu_test.index (resources/views/yuanqu_test/index.blade.php)", "param_count": 2, "params": ["sliders", "enterprises"], "type": "blade"}, {"name": "yuanqu_test.layouts.main (resources/views/yuanqu_test/layouts/main.blade.php)", "param_count": 12, "params": ["__env", "app", "errors", "sliders", "enterprises", "__currentLoopData", "slider", "loop", "index", "chunks", "chunk", "enterprise"], "type": "blade"}, {"name": "yuanqu_test.layouts.header (resources/views/yuanqu_test/layouts/header.blade.php)", "param_count": 12, "params": ["__env", "app", "errors", "sliders", "enterprises", "__currentLoopData", "slider", "loop", "index", "chunks", "chunk", "enterprise"], "type": "blade"}, {"name": "yuanqu_test.layouts.footer (resources/views/yuanqu_test/layouts/footer.blade.php)", "param_count": 12, "params": ["__env", "app", "errors", "sliders", "enterprises", "__currentLoopData", "slider", "loop", "index", "chunks", "chunk", "enterprise"], "type": "blade"}]}, "route": {"uri": "GET {association}", "middleware": "web, front_association", "controller": "App\\Http\\Controllers\\Home\\IndexController@index", "namespace": "App\\Http\\Controllers", "prefix": "/{association}", "where": [], "as": "front.login", "file": "<a href=\"phpstorm://open?file=/mnt/c/Users/<USER>/code/yuanqu/admin/app/Http/Controllers/Home/IndexController.php&line=102\">app/Http/Controllers/Home/IndexController.php:102-115</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00312, "accumulated_duration_str": "3.12ms", "statements": [{"sql": "select * from `associations` where `alias` = 'kejiyuan' and `associations`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["kejiyuan"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "middleware", "name": "front_association", "line": 68}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "line": 50}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "line": 78}], "duration": 0.00108, "duration_str": "1.08ms", "stmt_id": "middleware::front_association:68", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 0, "width_percent": 34.615}, {"sql": "select * from `sliders` where `show` = 1 and `association_id` = 5 and `sliders`.`deleted_at` is null order by `order` asc", "type": "query", "params": [], "bindings": ["1", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "/app/Http/Controllers/Home/IndexController.php", "line": 104}, {"index": 15, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 48}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721}], "duration": 0.0005600000000000001, "duration_str": "560μs", "stmt_id": "/app/Http/Controllers/Home/IndexController.php:104", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 34.615, "width_percent": 17.949}, {"sql": "select * from `enterprise` where `status` = 'normal' and exists (select * from `associations` inner join `association_enterprises` on `associations`.`id` = `association_enterprises`.`association_id` where `enterprise`.`id` = `association_enterprises`.`enterprise_id` and `status` = 'passed' and `associations`.`deleted_at` is null) and exists (select * from `associations` inner join `association_enterprises` on `associations`.`id` = `association_enterprises`.`association_id` where `enterprise`.`id` = `association_enterprises`.`enterprise_id` and `association_id` = 5 and `association_enterprises`.`status` = 'passed' and `associations`.`deleted_at` is null) and `enterprise`.`deleted_at` is null order by `recommend` desc, `order` asc, `id` desc limit 6", "type": "query", "params": [], "bindings": ["normal", "passed", "5", "passed"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "/app/Http/Controllers/Home/IndexController.php", "line": 112}, {"index": 15, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 48}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721}], "duration": 0.00148, "duration_str": "1.48ms", "stmt_id": "/app/Http/Controllers/Home/IndexController.php:112", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 52.564, "width_percent": 47.436}]}, "models": {"data": {"App\\Models\\Enterprise": 3, "App\\Models\\Slider": 2, "App\\Models\\Association": 1}, "count": 6}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "TDUMv4PtOg9drnklts1GloXvJkRpSg7OqATAFfaR", "_previous": "array:1 [\n  \"url\" => \"http://yuanqu.test/kejiyuan\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/kejiyuan/", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1062675032 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1062675032\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1516063967 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1516063967\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1052048783 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"706 characters\">XSRF-TOKEN=eyJpdiI6IlI0MEtnSDhwa0dIRkVINmR4cjhvQUE9PSIsInZhbHVlIjoia0REOG9yYlZCdGRtS0ZSY3JGYVdFYnN4WWI4RGNpWDFXejhIQ2FNRlFtUUN6UXZUY2VwUnN2TTFtdlZWTDNIbTdQOHJtSmxGTWo1ZmZlVEhuL096bEtzMHN5TlBNek9ta0JDcW8yUXVDQ0ZVQSs0bTZXeGFYcTFURnFmYlpzSWUiLCJtYWMiOiJjM2I3YTllZTUyMzA3MjFiMDQwNGFjYjUxMTc5YTkwNmJhYTQ2NWYyMzE1NmFkNTA0ZDgwZTFkY2RhMWE4MGFhIiwidGFnIjoiIn0%3D; _session=eyJpdiI6IklpeisrMVZ1Tmg1YWQ3MkUzZ29ERFE9PSIsInZhbHVlIjoiWnlvN2pPRWx3K1NHay94Tk1pTXdMV0RSZFFmY3pnaDJ1bklCSzRlR01xS0xFZWlPejhvRkt1OUduTG1ralpzVUF3MjVBWG9YS3ArVDJWY2xZaDl5NWNwZ0l1cHphRjZsMDk3MlVCZGdDNXVyRnhBMnh0dWVXZy9SbFA2T1llVTYiLCJtYWMiOiJhYmY5ODU1NjljMzY0ZGYxYzM2ZmRjNWZkMWY5ZDM4YWEwMWU4OGQ5MjAzNmQ2ZDNmYTc4Mjk0ODU2MGQzMjg2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">zh-CN,zh-TW;q=0.9,zh;q=0.8,en-US;q=0.7,en;q=0.6,ja;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">yuanqu.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1052048783\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1775755514 data-indent-pad=\"  \"><span class=sf-dump-note>array:36</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>USER</span>\" => \"<span class=sf-dump-str title=\"3 characters\">www</span>\"\n  \"<span class=sf-dump-key>HOME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">/home/<USER>/span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"706 characters\">XSRF-TOKEN=eyJpdiI6IlI0MEtnSDhwa0dIRkVINmR4cjhvQUE9PSIsInZhbHVlIjoia0REOG9yYlZCdGRtS0ZSY3JGYVdFYnN4WWI4RGNpWDFXejhIQ2FNRlFtUUN6UXZUY2VwUnN2TTFtdlZWTDNIbTdQOHJtSmxGTWo1ZmZlVEhuL096bEtzMHN5TlBNek9ta0JDcW8yUXVDQ0ZVQSs0bTZXeGFYcTFURnFmYlpzSWUiLCJtYWMiOiJjM2I3YTllZTUyMzA3MjFiMDQwNGFjYjUxMTc5YTkwNmJhYTQ2NWYyMzE1NmFkNTA0ZDgwZTFkY2RhMWE4MGFhIiwidGFnIjoiIn0%3D; _session=eyJpdiI6IklpeisrMVZ1Tmg1YWQ3MkUzZ29ERFE9PSIsInZhbHVlIjoiWnlvN2pPRWx3K1NHay94Tk1pTXdMV0RSZFFmY3pnaDJ1bklCSzRlR01xS0xFZWlPejhvRkt1OUduTG1ralpzVUF3MjVBWG9YS3ArVDJWY2xZaDl5NWNwZ0l1cHphRjZsMDk3MlVCZGdDNXVyRnhBMnh0dWVXZy9SbFA2T1llVTYiLCJtYWMiOiJhYmY5ODU1NjljMzY0ZGYxYzM2ZmRjNWZkMWY5ZDM4YWEwMWU4OGQ5MjAzNmQ2ZDNmYTc4Mjk0ODU2MGQzMjg2IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"56 characters\">zh-CN,zh-TW;q=0.9,zh;q=0.8,en-US;q=0.7,en;q=0.6,ja;q=0.5</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  \"<span class=sf-dump-key>HTTP_PRAGMA</span>\" => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"11 characters\">yuanqu.test</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"\"\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"11 characters\">yuanqu.test</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">56699</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"12 characters\">nginx/1.20.1</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"43 characters\">/mnt/c/Users/<USER>/code/yuanqu/admin/public</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_URI</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/kejiyuan/</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"53 characters\">/mnt/c/Users/<USER>/code/yuanqu/admin/public/index.php</span>\"\n  \"<span class=sf-dump-key>FCGI_ROLE</span>\" => \"<span class=sf-dump-str title=\"9 characters\">RESPONDER</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1750211128.5615</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1750211128</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1775755514\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-9151836 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TDUMv4PtOg9drnklts1GloXvJkRpSg7OqATAFfaR</span>\"\n  \"<span class=sf-dump-key>_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dOiJy0ZAZXxtiQ97LaRovROa48WaGw1obS8eq4I8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-9151836\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-757785330 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 18 Jun 2025 01:45:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ik5USGY1Y0VMRWZkR0s0aEZ0ZW8xd3c9PSIsInZhbHVlIjoid0lXcytGZXBhWi9zTnZXdy9HV2VOTE5UR2hRWS9EVFRHdXgxQ1BiNXBiR1RqTnNuZDMySnkvc0QxOUl1YXVMdHJnemlieUF6bE4zem9TakR0eDRmTEkzT2VzOVBTNm0rWmZtNHdROFA3RThIYm1rUXdjRmZsVUdVMjJYWVNKMWYiLCJtYWMiOiJhZGM1ODRhNjM5Zjk4MTY1MDFkNzY2MDQ5YzI5NWI4NmViZTc4ZTIyYjQxNjgzMTM1N2M3Y2Y5MzQ0OWMxMDVjIiwidGFnIjoiIn0%3D; expires=Wed, 18-Jun-2025 03:45:29 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"436 characters\">_session=eyJpdiI6Im4yOGFsOW1aNFBFRWRpYjlJSG9rdkE9PSIsInZhbHVlIjoiT2g0ay8wSVloMUdOdE9yR1hVQ3JwSmw2UU1OZitXczNidkE5Tk1NVlV4VUFXSzF0YXZlOTR6eVVGbjNZOEJ4Mmo0cm1CcDZieWdLTzEvNERDeFF4TUswTjZwTTJYNTdGTVVMQWp0bjdWMHhIV295dkwzblNsc0gweHk0ZWhkU08iLCJtYWMiOiJkMWM0YTg3MWQ1N2U0NGRmODYxNTE0NjY2ZWIxMDE0MDBiODQzZTY2YTZkNTY5ZWEyZDJmODAzOTY1ZjYwNzA2IiwidGFnIjoiIn0%3D; expires=Wed, 18-Jun-2025 03:45:29 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ik5USGY1Y0VMRWZkR0s0aEZ0ZW8xd3c9PSIsInZhbHVlIjoid0lXcytGZXBhWi9zTnZXdy9HV2VOTE5UR2hRWS9EVFRHdXgxQ1BiNXBiR1RqTnNuZDMySnkvc0QxOUl1YXVMdHJnemlieUF6bE4zem9TakR0eDRmTEkzT2VzOVBTNm0rWmZtNHdROFA3RThIYm1rUXdjRmZsVUdVMjJYWVNKMWYiLCJtYWMiOiJhZGM1ODRhNjM5Zjk4MTY1MDFkNzY2MDQ5YzI5NWI4NmViZTc4ZTIyYjQxNjgzMTM1N2M3Y2Y5MzQ0OWMxMDVjIiwidGFnIjoiIn0%3D; expires=Wed, 18-Jun-2025 03:45:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"408 characters\">_session=eyJpdiI6Im4yOGFsOW1aNFBFRWRpYjlJSG9rdkE9PSIsInZhbHVlIjoiT2g0ay8wSVloMUdOdE9yR1hVQ3JwSmw2UU1OZitXczNidkE5Tk1NVlV4VUFXSzF0YXZlOTR6eVVGbjNZOEJ4Mmo0cm1CcDZieWdLTzEvNERDeFF4TUswTjZwTTJYNTdGTVVMQWp0bjdWMHhIV295dkwzblNsc0gweHk0ZWhkU08iLCJtYWMiOiJkMWM0YTg3MWQ1N2U0NGRmODYxNTE0NjY2ZWIxMDE0MDBiODQzZTY2YTZkNTY5ZWEyZDJmODAzOTY1ZjYwNzA2IiwidGFnIjoiIn0%3D; expires=Wed, 18-Jun-2025 03:45:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-757785330\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1128320936 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TDUMv4PtOg9drnklts1GloXvJkRpSg7OqATAFfaR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://yuanqu.test/kejiyuan</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1128320936\", {\"maxDepth\":0})</script>\n"}}