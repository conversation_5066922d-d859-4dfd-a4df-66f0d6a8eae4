{"__meta": {"id": "Xfb80ac9a394408bbc490b4258427a510", "datetime": "2025-06-18 09:06:54", "utime": 1750208814.134817, "method": "GET", "uri": "/dahonglu", "ip": "127.0.0.1"}, "php": {"version": "8.0.26", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750208813.013092, "end": 1750208814.134831, "duration": 1.1217389106750488, "duration_str": "1.12s", "measures": [{"label": "Booting", "start": 1750208813.013092, "relative_start": 0, "end": 1750208813.725809, "relative_end": 1750208813.725809, "duration": 0.7127170562744141, "duration_str": "713ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1750208813.72582, "relative_start": 0.7127280235290527, "end": 1750208814.134833, "relative_end": 2.1457672119140625e-06, "duration": 0.409013032913208, "duration_str": "409ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 43217648, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 5, "templates": [{"name": "default.index (resources/views/default/index.blade.php)", "param_count": 2, "params": ["sliders", "enterprises"], "type": "blade"}, {"name": "default.layouts.main (resources/views/default/layouts/main.blade.php)", "param_count": 24, "params": ["__env", "app", "errors", "sliders", "enterprises", "__currentLoopData", "slider", "loop", "association", "category", "notices", "notice", "forums", "enterprises_chunk", "ens", "enterprise", "index", "forum", "news", "new", "mentors", "mentors_chunk", "links", "link"], "type": "blade"}, {"name": "default.layouts.seo (resources/views/default/layouts/seo.blade.php)", "param_count": 0, "params": [], "type": "blade"}, {"name": "default.layouts.header (resources/views/default/layouts/header.blade.php)", "param_count": 24, "params": ["__env", "app", "errors", "sliders", "enterprises", "__currentLoopData", "slider", "loop", "association", "category", "notices", "notice", "forums", "enterprises_chunk", "ens", "enterprise", "index", "forum", "news", "new", "mentors", "mentors_chunk", "links", "link"], "type": "blade"}, {"name": "default.layouts.footer (resources/views/default/layouts/footer.blade.php)", "param_count": 24, "params": ["__env", "app", "errors", "sliders", "enterprises", "__currentLoopData", "slider", "loop", "association", "category", "notices", "notice", "forums", "enterprises_chunk", "ens", "enterprise", "index", "forum", "news", "new", "mentors", "mentors_chunk", "links", "link"], "type": "blade"}]}, "route": {"uri": "GET {association}", "middleware": "web, front_association", "controller": "App\\Http\\Controllers\\Home\\IndexController@index", "namespace": "App\\Http\\Controllers", "prefix": "/{association}", "where": [], "as": "front.login", "file": "<a href=\"phpstorm://open?file=/mnt/c/Users/<USER>/code/yuanqu/admin/app/Http/Controllers/Home/IndexController.php&line=102\">app/Http/Controllers/Home/IndexController.php:102-115</a>"}, "queries": {"nb_statements": 18, "nb_failed_statements": 0, "accumulated_duration": 0.019689999999999996, "accumulated_duration_str": "19.69ms", "statements": [{"sql": "select * from `associations` where `alias` = 'dahonglu' and `associations`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON>"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "middleware", "name": "front_association", "line": 68}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "line": 50}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "line": 78}], "duration": 0.00158, "duration_str": "1.58ms", "stmt_id": "middleware::front_association:68", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 0, "width_percent": 8.024}, {"sql": "select * from `sliders` where `show` = 1 and `association_id` = 2 and `sliders`.`deleted_at` is null order by `order` asc", "type": "query", "params": [], "bindings": ["1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "/app/Http/Controllers/Home/IndexController.php", "line": 104}, {"index": 15, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 48}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721}], "duration": 0.00108, "duration_str": "1.08ms", "stmt_id": "/app/Http/Controllers/Home/IndexController.php:104", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 8.024, "width_percent": 5.485}, {"sql": "select * from `enterprise` where `status` = 'normal' and exists (select * from `associations` inner join `association_enterprises` on `associations`.`id` = `association_enterprises`.`association_id` where `enterprise`.`id` = `association_enterprises`.`enterprise_id` and `status` = 'passed' and `associations`.`deleted_at` is null) and exists (select * from `associations` inner join `association_enterprises` on `associations`.`id` = `association_enterprises`.`association_id` where `enterprise`.`id` = `association_enterprises`.`enterprise_id` and `association_id` = 2 and `association_enterprises`.`status` = 'passed' and `associations`.`deleted_at` is null) and `enterprise`.`deleted_at` is null order by `recommend` desc, `order` asc, `id` desc limit 6", "type": "query", "params": [], "bindings": ["normal", "passed", "2", "passed"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "/app/Http/Controllers/Home/IndexController.php", "line": 112}, {"index": 15, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 48}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721}], "duration": 0.0025099999999999996, "duration_str": "2.51ms", "stmt_id": "/app/Http/Controllers/Home/IndexController.php:112", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 13.509, "width_percent": 12.748}, {"sql": "select * from `categories` where `alias` = 'tongzhigonggao' and `association_id` = 2 and `categories`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["tongzhigonggao", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "aba35d87a3c7743298a8d82187182a4182ba0d98", "line": 27}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 20, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.00133, "duration_str": "1.33ms", "stmt_id": "view::aba35d87a3c7743298a8d82187182a4182ba0d98:27", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 26.257, "width_percent": 6.755}, {"sql": "select `articles`.*, `article_categories`.`category_id` as `pivot_category_id`, `article_categories`.`article_id` as `pivot_article_id` from `articles` inner join `article_categories` on `articles`.`id` = `article_categories`.`article_id` where `article_categories`.`category_id` = 18 and `show` = 1 and `association_id` = 2 and `articles`.`deleted_at` is null order by `top` desc, `order` asc, `created_at` desc limit 4", "type": "query", "params": [], "bindings": ["18", "1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "aba35d87a3c7743298a8d82187182a4182ba0d98", "line": 29}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 19, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.00217, "duration_str": "2.17ms", "stmt_id": "view::aba35d87a3c7743298a8d82187182a4182ba0d98:29", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 33.012, "width_percent": 11.021}, {"sql": "select * from `forums` where `show` = 1 and `association_id` = 2 order by `top` desc, `order` asc, `created_at` desc limit 4", "type": "query", "params": [], "bindings": ["1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "aba35d87a3c7743298a8d82187182a4182ba0d98", "line": 62}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 19, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.00126, "duration_str": "1.26ms", "stmt_id": "view::aba35d87a3c7743298a8d82187182a4182ba0d98:62", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 44.033, "width_percent": 6.399}, {"sql": "select * from `categories` where `alias` = 'x<PERSON><PERSON><PERSON>wen' and `association_id` = 2 and `categories`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "aba35d87a3c7743298a8d82187182a4182ba0d98", "line": 141}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 20, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.00069, "duration_str": "690μs", "stmt_id": "view::aba35d87a3c7743298a8d82187182a4182ba0d98:141", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 50.432, "width_percent": 3.504}, {"sql": "select `articles`.*, `article_categories`.`category_id` as `pivot_category_id`, `article_categories`.`article_id` as `pivot_article_id` from `articles` inner join `article_categories` on `articles`.`id` = `article_categories`.`article_id` where `article_categories`.`category_id` = 17 and `show` = 1 and `association_id` = 2 and `articles`.`deleted_at` is null order by `top` desc, `order` asc, `created_at` desc limit 4", "type": "query", "params": [], "bindings": ["17", "1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "aba35d87a3c7743298a8d82187182a4182ba0d98", "line": 143}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 19, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.00103, "duration_str": "1.03ms", "stmt_id": "view::aba35d87a3c7743298a8d82187182a4182ba0d98:143", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 53.936, "width_percent": 5.231}, {"sql": "select * from `mentors` where `show` = 1 and `association_id` = 2 order by `top` desc, `order` asc, `created_at` desc limit 5", "type": "query", "params": [], "bindings": ["1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "aba35d87a3c7743298a8d82187182a4182ba0d98", "line": 210}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 19, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.00095, "duration_str": "950μs", "stmt_id": "view::aba35d87a3c7743298a8d82187182a4182ba0d98:210", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 59.167, "width_percent": 4.825}, {"sql": "select * from `links` where `show` = 1 and `association_id` = 2 and `links`.`deleted_at` is null order by `order` desc, `created_at` desc", "type": "query", "params": [], "bindings": ["1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "aba35d87a3c7743298a8d82187182a4182ba0d98", "line": 250}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 19, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.0011200000000000001, "duration_str": "1.12ms", "stmt_id": "view::aba35d87a3c7743298a8d82187182a4182ba0d98:250", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 63.992, "width_percent": 5.688}, {"sql": "select * from `navs` where (`parent_id` = 0 or `parent_id` is null) and `show` = 1 and `association_id` = 2 and `navs`.`deleted_at` is null order by `order` asc", "type": "query", "params": [], "bindings": ["0", "1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "4b551dc7569e49db71ac2552133a3f418724512c", "line": 25}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 19, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.00127, "duration_str": "1.27ms", "stmt_id": "view::4b551dc7569e49db71ac2552133a3f418724512c:25", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 69.68, "width_percent": 6.45}, {"sql": "select * from `navs` where `parent_id` = 47 and `show` = 1 and `association_id` = 2 and `navs`.`deleted_at` is null order by `order` asc", "type": "query", "params": [], "bindings": ["47", "1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "4b551dc7569e49db71ac2552133a3f418724512c", "line": 31}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 19, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.00063, "duration_str": "630μs", "stmt_id": "view::4b551dc7569e49db71ac2552133a3f418724512c:31", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 76.13, "width_percent": 3.2}, {"sql": "select * from `navs` where `parent_id` = 14 and `show` = 1 and `association_id` = 2 and `navs`.`deleted_at` is null order by `order` asc", "type": "query", "params": [], "bindings": ["14", "1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "4b551dc7569e49db71ac2552133a3f418724512c", "line": 31}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 19, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.00069, "duration_str": "690μs", "stmt_id": "view::4b551dc7569e49db71ac2552133a3f418724512c:31", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 79.33, "width_percent": 3.504}, {"sql": "select * from `navs` where `parent_id` = 12 and `show` = 1 and `association_id` = 2 and `navs`.`deleted_at` is null order by `order` asc", "type": "query", "params": [], "bindings": ["12", "1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "4b551dc7569e49db71ac2552133a3f418724512c", "line": 31}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 19, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.00055, "duration_str": "550μs", "stmt_id": "view::4b551dc7569e49db71ac2552133a3f418724512c:31", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 82.834, "width_percent": 2.793}, {"sql": "select * from `navs` where `parent_id` = 13 and `show` = 1 and `association_id` = 2 and `navs`.`deleted_at` is null order by `order` asc", "type": "query", "params": [], "bindings": ["13", "1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "4b551dc7569e49db71ac2552133a3f418724512c", "line": 31}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 19, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.00068, "duration_str": "680μs", "stmt_id": "view::4b551dc7569e49db71ac2552133a3f418724512c:31", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 85.627, "width_percent": 3.454}, {"sql": "select * from `navs` where `parent_id` = 45 and `show` = 1 and `association_id` = 2 and `navs`.`deleted_at` is null order by `order` asc", "type": "query", "params": [], "bindings": ["45", "1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "4b551dc7569e49db71ac2552133a3f418724512c", "line": 31}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 19, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.00055, "duration_str": "550μs", "stmt_id": "view::4b551dc7569e49db71ac2552133a3f418724512c:31", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 89.081, "width_percent": 2.793}, {"sql": "select * from `navs` where `parent_id` = 46 and `show` = 1 and `association_id` = 2 and `navs`.`deleted_at` is null order by `order` asc", "type": "query", "params": [], "bindings": ["46", "1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "4b551dc7569e49db71ac2552133a3f418724512c", "line": 31}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 19, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.00067, "duration_str": "670μs", "stmt_id": "view::4b551dc7569e49db71ac2552133a3f418724512c:31", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 91.874, "width_percent": 3.403}, {"sql": "select * from `pages` where `alias` = '园区简介' limit 1", "type": "query", "params": [], "bindings": ["园区简介"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "1ab61f277331ad0356d6d880ff0067a8dad67ade", "line": 32}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 20, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.00093, "duration_str": "930μs", "stmt_id": "view::1ab61f277331ad0356d6d880ff0067a8dad67ade:32", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 95.277, "width_percent": 4.723}]}, "models": {"data": {"App\\Models\\Page": 1, "App\\Models\\Nav": 9, "App\\Models\\Link": 1, "App\\Models\\Forum": 4, "App\\Models\\Article": 7, "App\\Models\\Category": 2, "App\\Models\\Enterprise": 6, "App\\Models\\Slider": 1, "App\\Models\\Association": 1}, "count": 32}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "TDUMv4PtOg9drnklts1GloXvJkRpSg7OqATAFfaR", "_previous": "array:1 [\n  \"url\" => \"http://yuanqu.test/dahonglu\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/dahonglu", "status_code": "<pre class=sf-dump id=sf-dump-2065779097 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2065779097\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1406750358 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1406750358\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1973892725 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1973892725\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-799698310 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"706 characters\">XSRF-TOKEN=eyJpdiI6Ijc3dmpiUXFpVmJKMzc3MndoVWNjU0E9PSIsInZhbHVlIjoiN29jN1JiRXVUdnFKc1UyYWVZdWZOS3p3NnhUQjhLRFJKd2ZWdVNHcXRWRm8rZ0dxMUttdzVaQldtNG51UE5nWExXeW5sZE9rODdFeUNZbi9uRFJJdHU4d3JWY3kyVHQwb0xVV0tOMDBiTmJ3SzRkRHZxNXkwWS9LTGdPNG5TZzciLCJtYWMiOiJjNTNiNzE3MTQzYzIyNmY2ZWE4OWIwYTdkM2IxNGU0MDA2ZjRhODYzMTg0ODNhZTQ2ZjE2MWUzNjY1NjlkZjA0IiwidGFnIjoiIn0%3D; _session=eyJpdiI6IjR0Z2FVdFo4QU5VV21VNmJUNkxaVVE9PSIsInZhbHVlIjoiMFp4Y2prbUVGbGVyZHV0cnoxUHArWTJjMFdoWWdhWUl5UGJYUVdORGo1aFFiN24vMmExSlorSmpJWlJmRUs4eFQvcjBuMUljK0VpTDk0Q3JucW5Na2RLZklkb0xLSmt1MTkrKzlNQjcrMTZHZ2JkRUxpbEgyV1BtTjNpeFFRM1YiLCJtYWMiOiIyNTgzNGY5MzY0MzU3MzY0YzdiMjc2ZDYzNWU4YzY4ODc1OGFmODhmZmU2ZTJiZTRmYjZkMTAyNjYxMmYyZjFlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">zh-CN,zh-TW;q=0.9,zh;q=0.8,en-US;q=0.7,en;q=0.6,ja;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">http://yuanqu.test/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">yuanqu.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-799698310\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1946675344 data-indent-pad=\"  \"><span class=sf-dump-note>array:37</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>USER</span>\" => \"<span class=sf-dump-str title=\"3 characters\">www</span>\"\n  \"<span class=sf-dump-key>HOME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">/home/<USER>/span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"706 characters\">XSRF-TOKEN=eyJpdiI6Ijc3dmpiUXFpVmJKMzc3MndoVWNjU0E9PSIsInZhbHVlIjoiN29jN1JiRXVUdnFKc1UyYWVZdWZOS3p3NnhUQjhLRFJKd2ZWdVNHcXRWRm8rZ0dxMUttdzVaQldtNG51UE5nWExXeW5sZE9rODdFeUNZbi9uRFJJdHU4d3JWY3kyVHQwb0xVV0tOMDBiTmJ3SzRkRHZxNXkwWS9LTGdPNG5TZzciLCJtYWMiOiJjNTNiNzE3MTQzYzIyNmY2ZWE4OWIwYTdkM2IxNGU0MDA2ZjRhODYzMTg0ODNhZTQ2ZjE2MWUzNjY1NjlkZjA0IiwidGFnIjoiIn0%3D; _session=eyJpdiI6IjR0Z2FVdFo4QU5VV21VNmJUNkxaVVE9PSIsInZhbHVlIjoiMFp4Y2prbUVGbGVyZHV0cnoxUHArWTJjMFdoWWdhWUl5UGJYUVdORGo1aFFiN24vMmExSlorSmpJWlJmRUs4eFQvcjBuMUljK0VpTDk0Q3JucW5Na2RLZklkb0xLSmt1MTkrKzlNQjcrMTZHZ2JkRUxpbEgyV1BtTjNpeFFRM1YiLCJtYWMiOiIyNTgzNGY5MzY0MzU3MzY0YzdiMjc2ZDYzNWU4YzY4ODc1OGFmODhmZmU2ZTJiZTRmYjZkMTAyNjYxMmYyZjFlIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"56 characters\">zh-CN,zh-TW;q=0.9,zh;q=0.8,en-US;q=0.7,en;q=0.6,ja;q=0.5</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"19 characters\">http://yuanqu.test/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  \"<span class=sf-dump-key>HTTP_PRAGMA</span>\" => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"11 characters\">yuanqu.test</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"\"\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"11 characters\">yuanqu.test</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">51981</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"12 characters\">nginx/1.20.1</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"43 characters\">/mnt/c/Users/<USER>/code/yuanqu/admin/public</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_URI</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"9 characters\">/dahonglu</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"53 characters\">/mnt/c/Users/<USER>/code/yuanqu/admin/public/index.php</span>\"\n  \"<span class=sf-dump-key>FCGI_ROLE</span>\" => \"<span class=sf-dump-str title=\"9 characters\">RESPONDER</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1750208813.0131</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1750208813</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1946675344\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1023742126 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TDUMv4PtOg9drnklts1GloXvJkRpSg7OqATAFfaR</span>\"\n  \"<span class=sf-dump-key>_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dOiJy0ZAZXxtiQ97LaRovROa48WaGw1obS8eq4I8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1023742126\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-190648135 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 18 Jun 2025 01:06:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImUxa1FidE1ETzRjMThUR3V0S0haYUE9PSIsInZhbHVlIjoiaUU2OTREcGdFMHBSOGFyVE1PSXdTNnkzbld1eWprbHVkTkYwYy9KOWJWZUE5VGJ5cDFGWGVLQVBiREdocWtlZk9LRXFLWjNZaUl2czBJb2s1a2VCTThkLzhtUUxocDlrQzAzd002WTE5ZTVIRkJOMUlwQmRlNTQzaE1xaWJTa2MiLCJtYWMiOiIyNjdjYjg5MTU3ZjlmYWRlYTFlMmZkY2FiZTYyNTdlMzRkMzY2ZTU2MGZmYzY3NjYzMTM0MWNjOTllMWZmZjgzIiwidGFnIjoiIn0%3D; expires=Wed, 18-Jun-2025 03:06:54 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"436 characters\">_session=eyJpdiI6IjRJRjBNcDlQME1Ca0pIOTVqZE5PU3c9PSIsInZhbHVlIjoiK0pOM0tNdHQ2RGN1MCtQQ21iNUY0bjFPWDc2N3ZxMmlrQ1p1aForUkg1cnJSTGlIemo0SnNyL083WWF3c1FUM1NwNDBzWW8vcm8yUzhaUXhUVXhZR2ladU5mSlJ5UVdJMzlyTUR1YkwwcjdEWWp2blNQRGptZ2NUNDVZdDNVOCsiLCJtYWMiOiI3YjRiNzFjY2I1NjhhMjM2MjZmN2E5ZDU5MjgwMDkyOTNhMTI1YTI5NTk0NDUwMDY0ZThlNjY0ZTM5Mzk2OGMzIiwidGFnIjoiIn0%3D; expires=Wed, 18-Jun-2025 03:06:54 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImUxa1FidE1ETzRjMThUR3V0S0haYUE9PSIsInZhbHVlIjoiaUU2OTREcGdFMHBSOGFyVE1PSXdTNnkzbld1eWprbHVkTkYwYy9KOWJWZUE5VGJ5cDFGWGVLQVBiREdocWtlZk9LRXFLWjNZaUl2czBJb2s1a2VCTThkLzhtUUxocDlrQzAzd002WTE5ZTVIRkJOMUlwQmRlNTQzaE1xaWJTa2MiLCJtYWMiOiIyNjdjYjg5MTU3ZjlmYWRlYTFlMmZkY2FiZTYyNTdlMzRkMzY2ZTU2MGZmYzY3NjYzMTM0MWNjOTllMWZmZjgzIiwidGFnIjoiIn0%3D; expires=Wed, 18-Jun-2025 03:06:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"408 characters\">_session=eyJpdiI6IjRJRjBNcDlQME1Ca0pIOTVqZE5PU3c9PSIsInZhbHVlIjoiK0pOM0tNdHQ2RGN1MCtQQ21iNUY0bjFPWDc2N3ZxMmlrQ1p1aForUkg1cnJSTGlIemo0SnNyL083WWF3c1FUM1NwNDBzWW8vcm8yUzhaUXhUVXhZR2ladU5mSlJ5UVdJMzlyTUR1YkwwcjdEWWp2blNQRGptZ2NUNDVZdDNVOCsiLCJtYWMiOiI3YjRiNzFjY2I1NjhhMjM2MjZmN2E5ZDU5MjgwMDkyOTNhMTI1YTI5NTk0NDUwMDY0ZThlNjY0ZTM5Mzk2OGMzIiwidGFnIjoiIn0%3D; expires=Wed, 18-Jun-2025 03:06:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-190648135\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2095046614 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TDUMv4PtOg9drnklts1GloXvJkRpSg7OqATAFfaR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://yuanqu.test/dahonglu</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2095046614\", {\"maxDepth\":0})</script>\n"}}