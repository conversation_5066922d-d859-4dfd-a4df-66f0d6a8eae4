<!-- 文章详情 -->
<!DOCTYPE html>
<html>
  <head>
    <title>学校名称-部门名称</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="keywords" content="关键词1,关键词2,关键词3" />
    <meta name="description" content="描述" />
    <script type="text/javascript" src="js/unocss.runtime.js"></script>
    <link rel="stylesheet" type="text/css" href="css/common.css" />
    <link rel="stylesheet" type="text/css" href="css/detail.css" />
  </head>
  <body>
    <header>
      <div class="container">
        <!-- logo、搜索框 -->
        <div
          class="logo flex flex-col lg:flex-row justify-between gap-2"
          frag="面板02"
        >
          <a href="/" class="flex flex-col lg:flex-row items-center gap-4">
            <img src="images/logo2.png" alt="logo" />
          </a>
          <div class="search">
            <input type="text" placeholder="搜索" />
            <button>搜索</button>
          </div>
        </div>
        <!-- 导航栏 -->
        <nav frag="面板03">
          <!--[NaviStructBegin]-->
          <ul
            class="nav-list flex flex-wrap"
            frag="窗口31"
            portletmode="simpleSudyNavi"
          >
            <!--[NaviItemCycleBegin]-->
            <li class="flex-shrink-0 {级别样式} {选中样式}">
              <a href="list.html">首页</a>
            </li>
            <li class="flex-shrink-0 {级别样式} {选中样式}">
              <a href="list.html">园区介绍</a>
              <!--[MenuStructBegin]-->
              <ul class="sub-menu">
                <!--[MenuItemCycleBegin]-->
                <li class="sub-item {级别样式}">
                  <a class="sub-link" href="list.html" target="{打开方式}"
                    >通知公告</a
                  ><!--[SubMenuList]-->
                </li>
                <!--[MenuItemCycleEnd]-->
              </ul>
              <!--[MenuStructEnd]-->
            </li>
            <li class="flex-shrink-0 {级别样式} {选中样式}">
              <a href="list.html">通知公告</a>
            </li>
            <li class="flex-shrink-0 {级别样式} {选中样式}">
              <a href="list.html">园区动态</a>
            </li>
            <li class="flex-shrink-0 {级别样式} {选中样式}">
              <a href="list.html">园区服务</a>
            </li>
            <li class="flex-shrink-0 {级别样式} {选中样式}">
              <a href="list.html">园区企业</a>
            </li>
            <li class="flex-shrink-0 {级别样式} {选中样式}">
              <a href="list.html">技术转移</a>
            </li>
            <li class="flex-shrink-0 {级别样式} {选中样式}">
              <a href="list.html">办事中心</a>
            </li>
            <li class="flex-shrink-0 {级别样式} {选中样式}">
              <a href="list.html">联系我们</a>
            </li>
          </ul>
          <!--[NaviStructEnd]-->
        </nav>
      </div>
    </header>

    <div class="content">
      <div class="banner">
        <img src="images/banner.png" alt="banner" />
      </div>
      <div class="box">
        <div class="container grid grid-cols-4 lg:gap-25">
          <!-- 左侧栏 -->
          <div class="left-col col-span-4 lg:col-span-1" frag="面板05">
            <div class="left-box">
              <h3 frag="窗口51" portletmode="simpleColumnAnchor">
                <span frag="窗口内容">位置栏目</span>
              </h3>
              <div frag="窗口52" portletmode="simpleColumnList">
                <div frag="窗口内容">
                  <!--[ColumnStructBegin]-->
                  <ul class="left-list flex flex-col">
                    <!--[ColumnCycleBegin]-->
                    <li class="{级别样式} {选中样式}">
                      <a href="list.html">
                        通知公告
                        <img src="images/arrow-right.svg" class="icon" />
                      </a>
                    </li>
                    <!--[ColumnCycleEnd]-->
                  </ul>
                  <!--[ColumnStructEnd]-->
                </div>
              </div>
            </div>
          </div>
          <!-- 主内容区 -->
          <div class="main-col col-span-4 lg:col-span-3" frag="面板06">
            <div
              class="breadcrumb"
              frag="窗口61"
              portletmode="simpleColumnAttri"
            >
              <div frag="窗口内容">
                <span class="label">您的位置：</span>
                首页 / 通知公告
              </div>
            </div>
            <div class="main-box">
              <div class="empty hidden">
                <img src="images/empty.png" alt="empty" />
                <p>暂无内容</p>
              </div>

              <div
                class="article"
                frag="窗口62"
                portletmode="simpleArticleAttri"
              >
                <div frag="窗口内容">
                  <h1>
                    约图线程派置并极展圆石场专海战交决转或划年单道记华群约图线程派置并极展圆石场专海战交决转或划年单道记华群
                  </h1>
                  <div
                    class="article-info flex justify-center items-center gap-20"
                  >
                    <span class="info-item flex items-center gap-2">
                      <img src="images/time.svg" alt="time" class="icon" />
                      发布于 2023-06-07
                    </span>
                    <span class="info-item flex items-center gap-2">
                      <img src="images/eye.svg" alt="time" class="icon" />
                      浏览量：100
                    </span>
                  </div>
                  <div class="article-content">
                    化学化工学院功能材料研究所蔺红桃副教授在有机晶体材料低维结构的精准自组装及其集成光子学应用研究方面取得重要进展，构建了枝杈型、核壳型以及枝杈-核壳集成型的多级有机微结构。有机半导体晶体材料在有机发光二极管、有机场效应晶体管和有机固态激光器等光电器件中具有广阔的应用前景，具有多功能化的有机晶态多级低维结构的精确构筑对有机纳米光子学发展至关重要。由于晶体自组装过程中均匀/异构成核等问题，其精确构筑面临巨大挑战，同时，在构筑超致密电路过程中，存在的微米尺度上的精确操纵和传播光学信号问题也亟待解决。
                  </div>
                  <!-- <div class="article-attachments">
                    <ul class="attachments-list flex flex-col">
                      <li>
                        <img src="images/attachment.svg" alt="attachment" />
                        <a href="">附件1：关于支委岗位申报通知.doc</a>
                      </li>
                      <li>
                        <img src="images/attachment.svg" alt="attachment" />
                        <a href="">附件2：支委岗位申报表.doc</a>
                      </li>
                    </ul>
                  </div> -->
                </div>

                <!-- <div class="page-nav flex justify-between items-center">
                  <a href="" class="prev">
                    <img src="images/page-arrow-left.svg" class="icon" />
                    上一篇
                  </a>
                  <a href="" class="next">
                    下一篇
                    <img src="images/page-arrow-right.svg" class="icon" />
                  </a>
                </div> -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="link">
      <div class="container">
        <h2>友情链接：</h2>
        <ul>
          <li><a href="#">市科技局</a></li>
          <li><a href="#">山东省教育厅</a></li>
          <li><a href="#">山东省科技厅</a></li>
          <li><a href="#">国家自然科学基金委员会</a></li>
          <li><a href="#">科学技术部</a></li>
          <li><a href="#">国家科技管理信息系统公共服务平台</a></li>
          <li><a href="#">山东省科技云平台</a></li>
          <li><a href="#">淄博市发展和改革委员会</a></li>
          <li><a href="#">山东省发展和改革委员会</a></li>
        </ul>
      </div>
    </div>

    <footer class="footer">
      <div class="container">
        <div class="footer-top">
          <div class="footer-top-left">
            <img src="images/logo.png" alt="logo" />
          </div>
          <div class="footer-top-right">
            <ul>
              <li>
                <span class="footer-top-right-title">联系电话：</span>
                <span class="footer-top-right-content">0533-2781918</span>
              </li>

              <li>
                <span class="footer-top-right-title">邮箱：</span>
                <span class="footer-top-right-content"
                  ><EMAIL></span
                >
              </li>

              <li>
                <span class="footer-top-right-title">地址：</span>
                <span class="footer-top-right-content"
                  >山东省淄博市张店区人民路188号</span
                >
              </li>
            </ul>
          </div>
        </div>

        <div class="copyright">
          <p>
            Copyright © 2025 科技园. All Rights Reserved.
            &nbsp;&nbsp;&nbsp;&nbsp;技术支持：萌芽科技
          </p>
        </div>
      </div>
    </footer>

    <script>
      $(function () {
        // 如果 article-content 没有内容，插入提示
        if (!$(".article").children().length) {
          $(".empty").removeClass("hidden")
        }
      })
    </script>
  </body>
</html>
