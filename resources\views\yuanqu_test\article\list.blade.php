@extends('yuanqu_test.layouts.main')

@section('title', '{{ $category->name ?? "文章列表" }} - 科技园')

@section('meta')
    @if(isset($category))
        <meta name="keywords" content="{{ $category->keywords }}" />
        <meta name="description" content="{{ $category->description }}" />
    @endif
@endsection

@section('styles')
    <link rel="stylesheet" type="text/css" href="{{ asset('static/yuanqu_test/css/list.css') }}" />
@endsection

@section('content')
    <div class="content">
        <div class="banner">
            <img src="{{ asset('static/yuanqu_test/images/banner.png') }}" alt="banner" />
        </div>
        <div class="box">
            <div class="container grid grid-cols-4 lg:gap-25">
                <!-- 左侧栏 -->
                <div class="left-col col-span-4 lg:col-span-1">
                    <div class="left-box">
                        <h3>
                            <span>{{ $current_category->name ?? '栏目导航' }}</span>
                        </h3>
                        <div>
                            @if(isset($sidebar_categories) && count($sidebar_categories) > 0)
                                <ul class="left-list flex flex-col">
                                    @foreach($sidebar_categories as $sidebar_category)
                                        <li class="{{ isset($current_category) && $sidebar_category->id == $current_category->id ? 'active' : '' }}">
                                            <a href="{{ url('/' . $sidebar_category->alias) }}">
                                                {{ $sidebar_category->name }}
                                                <img src="{{ asset('static/yuanqu_test/images/arrow-right.svg') }}" class="icon" />
                                            </a>
                                        </li>
                                    @endforeach
                                </ul>
                            @else
                                <ul class="left-list flex flex-col">
                                    <li class="active">
                                        <a href="#">
                                            {{ $current_category->name ?? '当前栏目' }}
                                            <img src="{{ asset('static/yuanqu_test/images/arrow-right.svg') }}" class="icon" />
                                        </a>
                                    </li>
                                </ul>
                            @endif
                        </div>
                    </div>
                </div>
                
                <!-- 主内容区 -->
                <div class="main-col col-span-4 lg:col-span-3">
                    <div class="breadcrumb">
                        <div>
                            <span class="label">您的位置：</span>
                            <a href="{{ url('/') }}">首页</a> / 
                            @if(isset($current_category))
                                {{ $current_category->name }}
                            @else
                                文章列表
                            @endif
                        </div>
                    </div>
                    
                    <div class="main-box">
                        @if(isset($articles) && count($articles) > 0)
                            <ul class="data-list">
                                @foreach($articles as $index => $article)
                                    <li class="data-item n{{ $index + 1 }}">
                                        <a href="{{ url('/' . ($current_category->alias ?? 'article') . '/' . $article->id) }}">
                                            <span class="data-title">{{ $article->title }}</span>
                                            <span class="date">{{ $article->created_at->format('Y-m-d') }}</span>
                                        </a>
                                    </li>
                                @endforeach
                            </ul>
                            
                            @if(method_exists($articles, 'links'))
                                <div class="pagination-wrapper">
                                    {{ $articles->links() }}
                                </div>
                            @endif
                        @else
                            <div class="empty">
                                <img src="{{ asset('static/yuanqu_test/images/empty.png') }}" alt="empty" />
                                <p>暂无内容</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        $(function () {
            // 如果 main-box 没有内容，显示空状态
            if (!$(".data-list").length || $(".data-list li").length === 0) {
                $(".empty").removeClass("hidden");
            }
        });
    </script>
@endsection
