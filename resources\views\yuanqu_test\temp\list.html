<!-- 文章列表 -->
<!DOCTYPE html>
<html>
  <head>
    <title>学校名称-部门名称</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <script type="text/javascript" src="js/unocss.runtime.js"></script>
    <link rel="stylesheet" type="text/css" href="css/common.css" />
    <link rel="stylesheet" type="text/css" href="css/list.css" />
  </head>
  <body>
    <header>
      <div class="container">
        <!-- logo、搜索框 -->
        <div
          class="logo flex flex-col lg:flex-row justify-between gap-2"
          frag="面板02"
        >
          <a href="/" class="flex flex-col lg:flex-row items-center gap-4">
            <img src="images/logo2.png" alt="logo" />
          </a>
          <div class="search">
            <input type="text" placeholder="搜索" />
            <button>搜索</button>
          </div>
        </div>
        <!-- 导航栏 -->
        <nav frag="面板03">
          <!--[NaviStructBegin]-->
          <ul
            class="nav-list flex flex-wrap"
            frag="窗口31"
            portletmode="simpleSudyNavi"
          >
            <!--[NaviItemCycleBegin]-->
            <li class="flex-shrink-0 {级别样式} {选中样式}">
              <a href="list.html">首页</a>
            </li>
            <li class="flex-shrink-0 {级别样式} {选中样式}">
              <a href="list.html">园区介绍</a>
              <!--[MenuStructBegin]-->
              <ul class="sub-menu">
                <!--[MenuItemCycleBegin]-->
                <li class="sub-item {级别样式}">
                  <a class="sub-link" href="list.html" target="{打开方式}"
                    >通知公告</a
                  ><!--[SubMenuList]-->
                </li>
                <!--[MenuItemCycleEnd]-->
              </ul>
              <!--[MenuStructEnd]-->
            </li>
            <li class="flex-shrink-0 {级别样式} {选中样式}">
              <a href="list.html">通知公告</a>
            </li>
            <li class="flex-shrink-0 {级别样式} {选中样式}">
              <a href="list.html">园区动态</a>
            </li>
            <li class="flex-shrink-0 {级别样式} {选中样式}">
              <a href="list.html">园区服务</a>
            </li>
            <li class="flex-shrink-0 {级别样式} {选中样式}">
              <a href="list.html">园区企业</a>
            </li>
            <li class="flex-shrink-0 {级别样式} {选中样式}">
              <a href="list.html">技术转移</a>
            </li>
            <li class="flex-shrink-0 {级别样式} {选中样式}">
              <a href="list.html">办事中心</a>
            </li>
            <li class="flex-shrink-0 {级别样式} {选中样式}">
              <a href="list.html">联系我们</a>
            </li>
          </ul>
          <!--[NaviStructEnd]-->
        </nav>
      </div>
    </header>

    <div class="content">
      <div class="banner">
        <img src="images/banner.png" alt="banner" />
      </div>
      <div class="box">
        <div class="container grid grid-cols-4 lg:gap-25">
          <!-- 左侧栏 -->
          <div class="left-col col-span-4 lg:col-span-1" frag="面板05">
            <div class="left-box">
              <h3 frag="窗口51" portletmode="simpleColumnAnchor">
                <span frag="窗口内容">位置栏目</span>
              </h3>
              <div frag="窗口52" portletmode="simpleColumnList">
                <div frag="窗口内容">
                  <!--[ColumnStructBegin]-->
                  <ul class="left-list flex flex-col">
                    <!--[ColumnCycleBegin]-->
                    <li class="{级别样式} {选中样式}">
                      <a href="list.html">
                        通知公告
                        <img src="images/arrow-right.svg" class="icon" />
                      </a>
                    </li>
                    <!--[ColumnCycleEnd]-->
                  </ul>
                  <!--[ColumnStructEnd]-->
                </div>
              </div>
            </div>
          </div>
          <!-- 主内容区 -->
          <div class="main-col col-span-4 lg:col-span-3" frag="面板06">
            <div
              class="breadcrumb"
              frag="窗口61"
              portletmode="simpleColumnAttri"
            >
              <div frag="窗口内容">
                <span class="label">您的位置：</span>
                首页 / 通知公告
              </div>
            </div>
            <div class="main-box" frag="窗口62" portletmode="simpleList">
              <div frag="窗口内容">
                <ul class="data-list">
                  <!--[InfoCycleBegin]-->
                  <li class="data-item n{序号值}">
                    <span class="data-title">
                      约图线程派置并极展圆石场专海战交决转或划年单道记华群约图线程派置并极展圆石场专海战交决转或划年单道记华群
                    </span>
                    <span class="date">2023-06-07</span>
                  </li>
                  <!--[InfoCycleEnd]-->
                </ul>
              </div>
            </div>
            <div class="empty hidden">
              <img src="images/empty.png" alt="empty" />
              <p>暂无内容</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="link">
      <div class="container">
        <h2>友情链接：</h2>
        <ul>
          <li><a href="#">市科技局</a></li>
          <li><a href="#">山东省教育厅</a></li>
          <li><a href="#">山东省科技厅</a></li>
          <li><a href="#">国家自然科学基金委员会</a></li>
          <li><a href="#">科学技术部</a></li>
          <li><a href="#">国家科技管理信息系统公共服务平台</a></li>
          <li><a href="#">山东省科技云平台</a></li>
          <li><a href="#">淄博市发展和改革委员会</a></li>
          <li><a href="#">山东省发展和改革委员会</a></li>
        </ul>
      </div>
    </div>

    <footer class="footer">
      <div class="container">
        <div class="footer-top">
          <div class="footer-top-left">
            <img src="images/logo.png" alt="logo" />
          </div>
          <div class="footer-top-right">
            <ul>
              <li>
                <span class="footer-top-right-title">联系电话：</span>
                <span class="footer-top-right-content">0533-2781918</span>
              </li>

              <li>
                <span class="footer-top-right-title">邮箱：</span>
                <span class="footer-top-right-content"
                  ><EMAIL></span
                >
              </li>

              <li>
                <span class="footer-top-right-title">地址：</span>
                <span class="footer-top-right-content"
                  >山东省淄博市张店区人民路188号</span
                >
              </li>
            </ul>
          </div>
        </div>

        <div class="copyright">
          <p>
            Copyright © 2025 科技园. All Rights Reserved.
            &nbsp;&nbsp;&nbsp;&nbsp;技术支持：萌芽科技
          </p>
        </div>
      </div>
    </footer>

    <script>
      $(function () {
        // 如果 main-box 没有内容，插入提示
        if (!$(".main-box").children().length) {
          $(".empty").removeClass("hidden")
        }
      })
    </script>
  </body>
</html>
