<header class="header">
    <?php if(isset($show_banner) && $show_banner): ?>
    <div class="banner">
        <img src="<?php echo e(asset('static/yuanqu_test/images/banner.png'), false); ?>" alt="banner" />
    </div>
    <?php endif; ?>

    <div class="container mx-auto px-4">
        <!-- logo、搜索框 -->
        <div class="logo flex flex-col lg:flex-row justify-between gap-2">
            <a href="<?php echo e(url('/'), false); ?>" class="flex flex-col lg:flex-row items-center gap-4">
                <img src="<?php echo e(asset('static/yuanqu_test/images/' . (isset($logo_type) && $logo_type == 'white' ? 'logo2.png' : 'logo.png')), false); ?>" alt="logo" />
            </a>
            <div class="search">
                <form action="<?php echo e(url('/search'), false); ?>" method="GET">
                    <input type="text" name="q" placeholder="搜索" value="<?php echo e(request('q'), false); ?>" />
                    <button type="submit">搜索</button>
                </form>
            </div>
        </div>
        
        <!-- 导航栏 -->
        <nav>
            <ul class="nav-list flex flex-wrap">
                <li class="flex-shrink-0 <?php echo e(request()->is('/') ? 'active' : '', false); ?>">
                    <a href="<?php echo e(url('/'), false); ?>">首页</a>
                </li>
                <li class="flex-shrink-0 <?php echo e(request()->is('about*') ? 'active' : '', false); ?>">
                    <a href="<?php echo e(url('/about'), false); ?>">园区介绍</a>
                    <ul class="sub-menu">
                        <li class="sub-item">
                            <a class="sub-link" href="<?php echo e(url('/about/overview'), false); ?>">园区概况</a>
                        </li>
                        <li class="sub-item">
                            <a class="sub-link" href="<?php echo e(url('/about/history'), false); ?>">发展历程</a>
                        </li>
                    </ul>
                </li>
                <li class="flex-shrink-0 <?php echo e(request()->is('notice*') ? 'active' : '', false); ?>">
                    <a href="<?php echo e(url('/notice'), false); ?>">通知公告</a>
                </li>
                <li class="flex-shrink-0 <?php echo e(request()->is('news*') ? 'active' : '', false); ?>">
                    <a href="<?php echo e(url('/news'), false); ?>">园区动态</a>
                </li>
                <li class="flex-shrink-0 <?php echo e(request()->is('service*') ? 'active' : '', false); ?>">
                    <a href="<?php echo e(url('/service'), false); ?>">园区服务</a>
                </li>
                <li class="flex-shrink-0 <?php echo e(request()->is('enterprises*') ? 'active' : '', false); ?>">
                    <a href="<?php echo e(url('/enterprises'), false); ?>">园区企业</a>
                </li>
                <li class="flex-shrink-0 <?php echo e(request()->is('technology*') ? 'active' : '', false); ?>">
                    <a href="<?php echo e(url('/technology'), false); ?>">技术转移</a>
                </li>
                <li class="flex-shrink-0 <?php echo e(request()->is('office*') ? 'active' : '', false); ?>">
                    <a href="<?php echo e(url('/office'), false); ?>">办事中心</a>
                </li>
                <li class="flex-shrink-0 <?php echo e(request()->is('contact*') ? 'active' : '', false); ?>">
                    <a href="<?php echo e(url('/contact'), false); ?>">联系我们</a>
                </li>
            </ul>
        </nav>
    </div>
</header>
<?php /**PATH /mnt/c/Users/<USER>/code/yuanqu/admin/resources/views/yuanqu_test/layouts/header.blade.php ENDPATH**/ ?>