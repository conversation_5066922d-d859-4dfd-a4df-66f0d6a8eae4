.header {
  position: relative;
  width: 100%;
  height: 100px;
}

.header {
  height: 750px;
}

header .banner {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

header .banner img {
  width: 100%;
  height: 100%;
}

header nav .nav-list li a {
  color: rgba(255, 255, 255, 0.89);
}
header nav .nav-list li.selected a,
header nav .nav-list li a:hover {
  color: #fff;
}

.search button {
  border: 1px solid #ffffff;
}

body > .content {
  width: 100%;
  min-height: calc(100vh - 348px);
}

.section {
  padding: 75px 0;
}

.section-1 {
}

.swiper {
  position: relative;
  width: 100%;
  height: 413px;
}
.swiper .swiper-container {
  width: 100%;
  height: 100%;
}
.swiper .swiper-container .swiper-slide {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
}
.swiper .swiper-container .swiper-slide img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.swiper .swiper-container .swiper-slide .swiper-slide-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: calc(100% - 44px);
  margin: 27px 22px;
  background: rgba(255, 255, 255, 0.7);
  color: #2b2b2b;
  font-size: 18px;
  line-height: 28px;
  padding: 18px 20px;
}
.swiper .swiper-container .swiper-slide .date {
  position: absolute;
  top: 22px;
  left: 22px;
  padding: 3px 13px 2px 7px;
  background: url("../images/swiper-date-bg.png") no-repeat;
  background-size: 100% 100%;
  color: #ffffff;
  font-size: 14px;
}
.swiper .swiper-container .swiper-pagination {
  position: absolute;
  bottom: 12px;
  left: 20px;
}
.swiper .swiper-container .swiper-pagination .swiper-pagination-bullet {
  display: inline-block;
  width: 28px;
  height: 3px;
  background: #ffffff;
  margin: 0 4px;
  cursor: pointer;
}
.swiper
  .swiper-container
  .swiper-pagination
  .swiper-pagination-bullet.selected {
  background: #0068B7;
}



.data-box .title-wrap {
  padding: 5px 0;
}
.data-box .title-wrap .title {
  padding-right: 15px;
  border-bottom: 1px solid #f5f5f5;
  font-size: 20px;
  font-weight: bold;
}
.title h2 {
  color: #5b5e63;
  line-height: 45px;
  font-size: 20px;
  position: relative;
}
.title h2::after {
  content: "";
  display: block;
  height: 3px;
  background-color: #0068B7;
  bottom: 0;
}
.title .sub-title {
  font-weight: bold;
  font-size: 20px;
  color: #5b5e63;
  opacity: 0.6;
}

.data-box {
  position: relative;
}
.data-box .title-wrap .tab-menu {
  width: 100%;
  /* overflow: visible;
  flex-wrap: wrap; */
  border-bottom: 1px solid #f5f5f5;
}
.data-box .title-wrap .tab-menu::-webkit-scrollbar {
  display: none;
}
.data-box .title-wrap .tab-menu li {
  flex-shrink: 0;
  line-height: 49px;
  margin-right: 12px;
  font-size: 16px;
  color: #5b5e63;
  cursor: pointer;
}
.data-box .title-wrap .tab-menu li.selected {
  font-size: 20px;
  font-weight: bold;
  border-bottom: 3px solid #0068B7;
  position: relative;
  overflow: visible;
}
/* 底部中央小三角 */
.data-box .title-wrap .tab-menu li.selected::after {
  content: "";
  display: block;
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #0068B7;
  position: absolute;
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
}

.data-box .banner {
  width: 100%;
  height: 92px;
  margin-top: 16px;
  margin-bottom: 6px;
}
.data-box.banner img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.data-block .more {
  position: absolute;
  top: 10px;
  right: 0;
  margin: 0;
  width: 40px;
  height: 40px;
}

.data-block .more a {
  display: block;
  width: 100%;
  height: 100%;
  background-image: url("../images/more.svg");
  background-size: 100% 100%;
}
.data-block .more a span {
  display: none;
}


.block-title {
  font-size: 26px;
  font-weight: 400;
  line-height: 45px;
  color: #555555;
  margin-top: 0;
}

.block-subtitle {
  font-size: 26px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  font-weight: bold;
  color: #e5e5e6;
  line-height: 45px;
}

.block-more-btn {
  display: block;
  width: 146px;
  height: 43px;
  border-radius: 22px 22px 22px 22px;
  opacity: 1;
  border: 1px solid #ebebeb;
  text-align: center;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #999999;
  line-height: 43px;
}

.block-more-btn:hover {
  color: #2f7ad2;
  border-color: #2f7ad2;
}




.enterprise-list .enterprise-item {
  display: inline-flex;
  flex-direction: column;
  height: 202px;
  padding: 25px;
  box-sizing: border-box;
  background-color: #ffffff;
  overflow: hidden;
}

.enterprise-item:hover {
  border-bottom: 1px solid #2f7ad2;
  box-shadow: 0px 3px 48px 1px rgba(0, 0, 0, 0.06);
}

.enterprise-item .logo {
  width: 75px;
  height: 75px;
  margin-bottom: 23px;
  border-radius: 50%;
  flex-shrink: 0;
}

.enterprise-item .info {
  font-size: 18px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #555555;
}

.enterprise-item .info .name {
  font-size: 14px;
  font-weight: 400;
  color: #555555;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.enterprise-item:hover .info .name {
  font-weight: bold;
}

.enterprise-item .info .direction {
  margin: 0;
  color: #999999;
  font-size: 14px;
  margin-top: 10px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}
