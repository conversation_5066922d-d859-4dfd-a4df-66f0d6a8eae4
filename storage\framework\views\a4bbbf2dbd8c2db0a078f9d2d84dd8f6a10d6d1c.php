<?php $__env->startSection('title', '科技园 - 首页'); ?>

<?php $__env->startSection('styles'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('static/yuanqu_test/css/index.css'), false); ?>" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="content">
        <section class="section section-1">
            <div class="container grid grid-cols-1 lg:grid-cols-3 gap-8 mx-auto px-4">
                <!-- 轮播图 -->
                <div class="swiper">
                    <div class="swiper-container">
                        <div class="w-full h-full">
                            <?php if(isset($sliders) && count($sliders) > 0): ?>
                                <?php $__currentLoopData = $sliders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $slider): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="swiper-slide">
                                        <a href="<?php echo e($slider->url ?? '#', false); ?>" target="_blank">
                                            <img src="<?php echo e(asset('storage/' . $slider->image), false); ?>" alt="<?php echo e($slider->title, false); ?>" />
                                            <div class="swiper-slide-content">
                                                <?php echo e($slider->title, false); ?>

                                            </div>
                                            <div class="date"><?php echo e($slider->created_at->format('Y-m-d'), false); ?></div>
                                        </a>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php else: ?>
                                <div class="swiper-slide">
                                    <a href="#" target="_blank">
                                        <img src="<?php echo e(asset('static/yuanqu_test/images/cover.png'), false); ?>" alt="默认轮播图" />
                                        <div class="swiper-slide-content">
                                            欢迎来到科技园
                                        </div>
                                        <div class="date"><?php echo e(date('Y-m-d'), false); ?></div>
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="swiper-pagination">
                            <?php if(isset($sliders) && count($sliders) > 0): ?>
                                <?php $__currentLoopData = $sliders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $slider): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <span class="swiper-pagination-bullet <?php echo e($index == 0 ? 'swiper-pagination-bullet-active' : '', false); ?>"></span>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php else: ?>
                                <span class="swiper-pagination-bullet swiper-pagination-bullet-active"></span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- 园区动态 -->
                <div class="data-box flex flex-col">
                    <div class="title-wrap flex flex-shrink-0 items-center justify-between gap-2">
                        <div class="title flex items-center gap-4">
                            <h2>园区动态</h2>
                            <span class="sub-title">News</span>
                        </div>
                        <div class="more flex-shrink-0">
                            <a href="<?php echo e(url('/news'), false); ?>">
                                <img src="<?php echo e(asset('static/yuanqu_test/images/more.svg'), false); ?>" alt="更多" />
                            </a>
                        </div>
                    </div>
                    <div class="flex-1">
                        <ul class="data-list">
                            <?php if(isset($news) && count($news) > 0): ?>
                                <?php $__currentLoopData = $news; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $article): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li class="data-item i<?php echo e($index + 1, false); ?>">
                                        <a href="<?php echo e(url('/news/' . $article->id), false); ?>">
                                            <span class="data-title"><?php echo e($article->title, false); ?></span>
                                            <span class="date"><?php echo e($article->created_at->format('Y-m-d'), false); ?></span>
                                        </a>
                                    </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php else: ?>
                                <li class="data-item i1">
                                    <span class="data-title">暂无园区动态</span>
                                    <span class="date"><?php echo e(date('Y-m-d'), false); ?></span>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </div>
                </div>

                <!-- 通知公告 -->
                <div class="data-box flex flex-col">
                    <div class="title-wrap flex flex-shrink-0 items-center justify-between gap-2">
                        <div class="title flex items-center gap-4">
                            <h2>通知公告</h2>
                            <span class="sub-title">Notice</span>
                        </div>
                        <div class="more flex-shrink-0">
                            <a href="<?php echo e(url('/notice'), false); ?>">
                                <img src="<?php echo e(asset('static/yuanqu_test/images/more.svg'), false); ?>" alt="更多" />
                            </a>
                        </div>
                    </div>
                    <div class="flex-1">
                        <ul class="data-list">
                            <?php if(isset($notices) && count($notices) > 0): ?>
                                <?php $__currentLoopData = $notices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $article): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li class="data-item i<?php echo e($index + 1, false); ?>">
                                        <a href="<?php echo e(url('/notice/' . $article->id), false); ?>">
                                            <span class="data-title"><?php echo e($article->title, false); ?></span>
                                            <span class="date"><?php echo e($article->created_at->format('Y-m-d'), false); ?></span>
                                        </a>
                                    </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php else: ?>
                                <li class="data-item i1">
                                    <span class="data-title">暂无通知公告</span>
                                    <span class="date"><?php echo e(date('Y-m-d'), false); ?></span>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <div class="section section-2">
            <div class="container grid grid-cols-1 lg:grid-cols-2 gap-8 mx-auto px-4">
                <!-- 园区企业 -->
                <div class="w-full flex flex-wrap">
                    <div class="label w-full md:w-1/4">
                        <div class="block-area">
                            <h3 class="block-title">园区企业</h3>
                            <h4 class="block-subtitle">Enterprises</h4>
                            <a class="block-more-btn" href="<?php echo e(url('/enterprises'), false); ?>">查看全部</a>
                        </div>
                        <div class="enterprise-swiper-pagination swiper-pagination-clickable swiper-pagination-bullets swiper-pagination-horizontal">
                            <?php if(isset($enterprises) && count($enterprises) > 0): ?>
                                <?php $chunks = array_chunk($enterprises->toArray(), 3); ?>
                                <?php $__currentLoopData = $chunks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $chunk): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <span class="swiper-pagination-bullet <?php echo e($index == 0 ? 'swiper-pagination-bullet-active' : '', false); ?>" 
                                          tabindex="0" role="button" aria-label="Go to slide <?php echo e($index + 1, false); ?>" 
                                          <?php echo e($index == 0 ? 'aria-current="true"' : '', false); ?>></span>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php else: ?>
                                <span class="swiper-pagination-bullet swiper-pagination-bullet-active" 
                                      tabindex="0" role="button" aria-label="Go to slide 1" aria-current="true"></span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="enterprise-content-wrapper enterprise-content-swiper-container w-full md:w-3/4 swiper-initialized swiper-horizontal swiper-pointer-events swiper-backface-hidden">
                        <div class="enterprise-list-wrapper swiper-wrapper">
                            <?php if(isset($enterprises) && count($enterprises) > 0): ?>
                                <?php $chunks = array_chunk($enterprises->toArray(), 3); ?>
                                <?php $__currentLoopData = $chunks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $chunk): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="enterprise-list swiper-slide <?php echo e($index == 0 ? 'swiper-slide-active' : '', false); ?>" 
                                         style="grid-template-columns: repeat(2, 1fr);" role="group" aria-label="<?php echo e($index + 1, false); ?> / <?php echo e(count($chunks), false); ?>">
                                        <?php $__currentLoopData = $chunk; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $enterprise): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <a class="enterprise-item" href="<?php echo e(url('/enterprises/' . $enterprise['id']), false); ?>">
                                                <img class="logo" src="<?php echo e(asset('storage/' . $enterprise['logo']), false); ?>" alt="<?php echo e($enterprise['name'], false); ?>" />
                                                <div class="info">
                                                    <h4 class="name"><?php echo e($enterprise['name'], false); ?></h4>
                                                    <div class="direction"><?php echo e($enterprise['industry'] ?? '其他行业', false); ?></div>
                                                </div>
                                            </a>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php else: ?>
                                <div class="enterprise-list swiper-slide swiper-slide-active" 
                                     style="grid-template-columns: repeat(2, 1fr);" role="group" aria-label="1 / 1">
                                    <div class="enterprise-item">
                                        <div class="info">
                                            <h4 class="name">暂无企业信息</h4>
                                            <div class="direction">敬请期待</div>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                        <span class="swiper-notification" aria-live="assertive" aria-atomic="true"></span>
                    </div>
                </div>

                <!-- 科研成果 -->
                <div class="data-box flex flex-col">
                    <div class="title-wrap flex flex-shrink-0 items-center justify-between gap-2">
                        <div class="title flex items-center gap-4">
                            <h2>科研成果</h2>
                            <span class="sub-title">Achievement</span>
                        </div>
                        <div class="more flex-shrink-0">
                            <a href="<?php echo e(url('/achievement'), false); ?>">
                                <img src="<?php echo e(asset('static/yuanqu_test/images/more.svg'), false); ?>" alt="更多" />
                            </a>
                        </div>
                    </div>
                    <div class="flex-1">
                        <ul class="data-list">
                            <?php if(isset($achievements) && count($achievements) > 0): ?>
                                <?php $__currentLoopData = $achievements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $article): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li class="data-item i<?php echo e($index + 1, false); ?>">
                                        <a href="<?php echo e(url('/achievement/' . $article->id), false); ?>">
                                            <span class="data-title"><?php echo e($article->title, false); ?></span>
                                            <span class="date"><?php echo e($article->created_at->format('Y-m-d'), false); ?></span>
                                        </a>
                                    </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php else: ?>
                                <li class="data-item i1">
                                    <span class="data-title">暂无科研成果</span>
                                    <span class="date"><?php echo e(date('Y-m-d'), false); ?></span>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <script src="<?php echo e(asset('static/yuanqu_test/js/index.js'), false); ?>"></script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('yuanqu_test.layouts.main', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /mnt/c/Users/<USER>/code/yuanqu/admin/resources/views/yuanqu_test/index.blade.php ENDPATH**/ ?>