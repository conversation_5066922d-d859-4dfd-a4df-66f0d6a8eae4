

<?php $__env->startSection('head'); ?>
    <title>111首页</title>
    <link rel="stylesheet" href="<?php echo e(asset('static/default/css/index.css'), false); ?>"/>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <!-- 轮播图 -->
    <div class="swiper-container">
        <div class="swiper-wrapper">
            <?php $__currentLoopData = $sliders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $slider): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="swiper-slide">
                    <img src="<?php echo e(user_front_image($slider->image), false); ?>"
                         alt=""/>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
        <!-- 如果需要分页器 -->
        <div class="swiper-pagination"></div>

        <div class="bottom-cover mobile-hidden" style="background: #f8f8f8">
        </div>
    </div>

    <?php
        global $association;
        $category = \App\Models\Category::query()->where('alias',$association->config['notice_category_alias'] ?? 0)->first();
        if ($category){
            $notices = $category->articles()->where("show",1)->orderByDesc("top")->orderBy("order")->orderByDesc("created_at")->take(4)->get();
        }else{
            $notices = [];
        }
    ?>
        <!-- 平台公告 -->
    <div class="notice">
        <div class="container">
            <h3>
                <span>通知公告111</span>
            </h3>
            <div class="divider mobile-hidden"></div>
            <div class="notice-swiper-container">
                <ul class="notice-list swiper-wrapper">
                    <?php $__currentLoopData = $notices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notice): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li class="swiper-slide">
                            <a href="<?php echo e(association_url($category->alias), false); ?>?category_id=<?php echo e($category->id, false); ?>&article_id=<?php echo e($notice->id, false); ?>">
                                <span class="title"><?php echo e($notice->title, false); ?></span>
                                <span
                                    class="date"><?php echo e(\Illuminate\Support\Carbon::parse($notice->created_at)->format('m/d'), false); ?></span>
                            </a>
                        </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
            </div>
            <a class="more" href="<?php echo e(association_url($category->alias), false); ?>">
                <img class="icon" src="<?php echo e(asset('static/default/images/more.png'), false); ?>" alt=""/>
                更多
            </a>
        </div>
    </div>
    <!-- 企业库 -->
    <?php
        $forums = \App\Models\Forum::query()->orderByDesc('top')->orderBy('order')->orderByDesc('created_at')->where('show',1)->take(4)->get();
    ?>
    <div class="enterprise">
        <div class="container row" style="padding: 20px 0px 70px">
            <div class="col-12 col-lg-8 row">
                <div class="label col-12 col-md-3">
                    <div class="block-area">
                        <h3 class="block-title">入驻企业</h3>
                        <h4 class="block-subtitle">Enterprises</h4>
                        <a class="block-more-btn" href="<?php echo e(association_url('enterprises'), false); ?>">查看全部</a>
                    </div>
                    <div class="enterprise-swiper-pagination"></div>
                </div>
                <div
                    class="enterprise-content-wrapper enterprise-content-swiper-container col-12 col-md-8"
                >
                    <div class="enterprise-list-wrapper swiper-wrapper">
                        <?php
                            // $enterprises 分成多个数组，每个数组有6个元素
                            $enterprises_chunk = array_chunk($enterprises->toArray(), 4);
                        ?>
                        <?php $__currentLoopData = $enterprises_chunk; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ens): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="enterprise-list swiper-slide" style="grid-template-columns: repeat(2, 1fr)">
                                <?php $__currentLoopData = $ens; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $enterprise): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <a class="enterprise-item"
                                    href="<?php echo e(association_route('front.enterprise_detail_short',['id'=>$enterprise['id']]), false); ?>">
                                        <img class="logo" src="<?php echo e(user_front_image($enterprise['logo']), false); ?>" alt=""/>
                                        <div class="info">
                                            <h4 class="name"><?php echo e($enterprise['name'], false); ?></h4>
                                            <div class="direction"><?php echo e($enterprise['industry'], false); ?></div>
                                        </div>
                                    </a>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
            <div class="col-12 col-lg-4 row">
                <div class="news-block">
                    <div style="display: flex;justify-content: space-between;">
                        <div>
                            <div class="title">
                                <h3 class="block-title">创业论坛</h3>
                            </div>
                            <div class="block-subtitle subtitle">Forums</div>
                        </div>
                        <a class="block-more-btn" href="<?php echo e(association_url('forums'), false); ?>">查看全部</a>
                    </div>

                    <ul class="forum-list">
                        <!-- 图片、标题、摘要、日期 -->
                        <?php $__currentLoopData = $forums; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $forum): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if($index <= 1): ?>
                                <li class="forum-item">
                                    <a href="<?php echo e(association_route('front.forum_detail',['id'=>$forum->id]), false); ?>">
                                        <h4 class="title"><?php echo e($forum->title, false); ?></h4>
                                        <div class="info">
                                            <span class="info-item">主讲人：<?php echo e($forum->speaker, false); ?></span>
                                            <span class="info-item">举办时间：<?php echo e($forum->time, false); ?></span>
                                            <span class="info-item">举办地点：<?php echo e($forum->address, false); ?></span>
                                        </div>
                                    </a>
                                </li>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>
            </div>
        </div>
    </div>


        <!-- 园区动态、创业论坛 -->


    <!-- 园区动态、创业论坛 -->
    <?php
        global $association;
        $category = \App\Models\Category::query()->where('alias',$association->config['news_category_alias'] ?? '')->first();
        if ($category){
            $news = $category->articles()->where("show",1)->orderByDesc("top")->orderBy("order")->orderByDesc("created_at")->take(4)->get();
        }else{
            $news = [];
        }
    ?>
    <div class="news">
        <div class="container">
            <div class="row">
                <div class="news-block col-md-6">
                    <div class="title">
                        <h3 class="block-title">园区动态</h3>
                    </div>
                    <div class="block-subtitle subtitle">Trends</div>
                    <div class="news-content">
                        <!-- 图片、标题、摘要、日期 -->
                        <?php $__currentLoopData = $news; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index =>  $new): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if($index <= 0): ?>
                                <div class="news-item">
                                    <a href="<?php echo e(association_url($category->alias), false); ?>?article_id=<?php echo e($new->id, false); ?>">
                                        <?php if($new->image): ?>
                                            <img class="cover" src="<?php echo e(user_front_image($new->image), false); ?>" alt=""/>
                                        <?php else: ?>
                                            <img class="cover"
                                                 src="<?php echo e(asset('static/default/images/article-default.png'), false); ?>" alt=""/>
                                        <?php endif; ?>
                                        <h4 class="title">
                                            <?php echo e($new->title, false); ?>

                                        </h4>
                                        <p class="content">
                                            <?php echo \Illuminate\Support\Str::limit(strip_tags($new->content),200); ?>

                                        </p>
                                        <div
                                            class="date"><?php echo e(\Illuminate\Support\Carbon::parse($new->created_at)->format('m-d'), false); ?></div>
                                    </a>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>

                <div class="news-block col-md-6">
                    <div class="title" style="justify-content: flex-end">
                        <a class="block-more-btn" href="<?php echo e(association_url($category->alias ?? ''), false); ?>">查看全部</a>
                    </div>
                    <ul class="forum-list">
                        <?php $__currentLoopData = $news; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $new): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if($index > 0): ?>
                                <li class="forum-item">
                                    <a href="<?php echo e(association_url($category->alias), false); ?>?article_id=<?php echo e($new->id, false); ?>">
                                        <h4 class="title"><?php echo e($new->title, false); ?></h4>
                                        <div class="info">
                                        <span
                                            class="info-item"><?php echo e(\Illuminate\Support\Str::limit(strip_tags($new->content),100), false); ?></span>
                                        </div>
                                    </a>
                                </li>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <!-- 导师库 -->
    <?php
        $mentors = \App\Models\Mentor::query()->orderByDesc('top')->orderBy('order')->orderByDesc('created_at')->where('show',1)->take(5)->get();
    ?>
    <div class="tutor">
        <div class="label"></div>
        <div class="container row">
            <div class=" col-12 col-lg-3" style="display: flex;flex-direction: column;justify-content: space-between;">
                <div class="block-area">
                    <h3 class="block-title">导师库</h3>
                    <h4 class="block-subtitle">Tutors</h4>
                    <a class="block-more-btn" href="<?php echo e(association_url('mentors'), false); ?>">查看全部</a>
                </div>
                <div class="tutor-swiper-pagination"></div>
            </div>

            <div class="tutor-content-wrapper tutor-content-swiper-container col-12 col-lg-9">
                <div class="tutor-list-wrapper swiper-wrapper">
                    <?php
                        // $tutors 分成多个数组，每个数组有3个元素
                        $mentors_chunk = array_chunk($mentors->toArray(), 3);
                    ?>
                    <?php $__currentLoopData = $mentors_chunk; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $mentors): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="tutor-list swiper-slide">
                            <?php $__currentLoopData = $mentors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $mentor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <a class="tutor-item" href="<?php echo e(association_route('front.mentor',['id'=>$mentor['id']]), false); ?>">
                                    <img class="photo" src="<?php echo e(user_front_image($mentor['image']), false); ?>" alt=""/>
                                    <div class="info">
                                        <h4 class="name"><?php echo e($mentor['name'], false); ?></h4>
                                        <div class="direction"><?php echo e($mentor['type']['name'] ?? '', false); ?></div>
                                    </div>
                                    <div class="bottom-bg"></div>
                                </a>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
    </div>

    <?php
        $links = \App\Models\Link::query()->orderByDesc('order')->orderByDesc('created_at')->where('show',1)->get();
    ?>
        <!-- 友情链接 -->
    <div class="links">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="links">
                        <span>友情链接：</span>
                        <ul>
                            <?php $__currentLoopData = $links; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $link): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li><a target="_blank" href="<?php echo e($link->url, false); ?>"><?php echo e($link->title, false); ?></a></li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('bottom'); ?>

    <script>
        var mySwiper = new Swiper(".swiper-container", {
            autoplay: true, //可选选项，自动滑动
            pagination: {
                el: ".swiper-pagination",
                clickable: true,
            },
        })

        var noticeSwiper = new Swiper(".notice-swiper-container", {
            autoplay: true, //可选选项，自动滑动
            direction: "vertical",
            loop: true,
        })

        var tutorSwiper = new Swiper(".tutor-content-swiper-container", {
            direction: "vertical",
            pagination: {
                el: ".tutor-swiper-pagination",
                clickable: true,
            },
        })

        var enterpriseSwiper = new Swiper(
            ".enterprise-content-swiper-container",
            {
                pagination: {
                    el: ".enterprise-swiper-pagination",
                    clickable: true,
                },
            }
        )

        var parksSwiper = new Swiper(".parks-content-swiper-container", {
            on: {
                slideChangeTransitionStart: function () {
                    console.log("slideChangeTransitionStart", this.activeIndex)
                    $(".tab-item").removeClass("active")
                    $(".tab-item").eq(this.activeIndex).addClass("active")
                    $(".swiper-tabs").animate(
                        {scrollLeft: this.activeIndex * 100},
                        500
                    )
                },
            },
        })
        $(".tab-item").click(function () {
            console.log("click", $(this).index())
            parksSwiper.slideTo($(this).index())
            $(".swiper-tabs").animate({scrollLeft: $(this).index() * 100}, 500)
        })
        // parksPrev parksNext
        $("#parksPrev").click(function () {
            console.log("prev", parksSwiper)
            parksSwiper.slidePrev()
        })
        $("#parksNext").click(function () {
            console.log("next", parksSwiper)
            parksSwiper.slideNext()
        })
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('default.layouts.main', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /mnt/c/Users/<USER>/code/yuanqu/admin/resources/views/yuanqu_test/index.blade.php ENDPATH**/ ?>