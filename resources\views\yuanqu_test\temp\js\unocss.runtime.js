/**
 * Skipped minification because the original files appears to be already minified.
 * Original file: /npm/@unocss/runtime@0.61.0/uno.global.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
"use strict";(()=>{var ja=Object.defineProperty;var za=(e,t)=>{for(var r in t)ja(e,r,{get:t[r],enumerable:!0})};function ie(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function Q(e){let t=e.length,r=-1,n,o="",i=e.charCodeAt(0);for(;++r<t;){if(n=e.charCodeAt(r),n===0){o+="\uFFFD";continue}if(n===37){o+="\\%";continue}if(n===44){o+="\\,";continue}if(n>=1&&n<=31||n===127||r===0&&n>=48&&n<=57||r===1&&n>=48&&n<=57&&i===45){o+=`\\${n.toString(16)} `;continue}if(r===0&&t===1&&n===45){o+=`\\${e.charAt(r)}`;continue}if(n>=128||n===45||n===95||n>=48&&n<=57||n>=65&&n<=90||n>=97&&n<=122){o+=e.charAt(r);continue}o+=`\\${e.charAt(r)}`}return o}var et=Q;function O(e=[]){return Array.isArray(e)?e:[e]}function be(e){return Array.from(new Set(e))}function Cn(e,t){return e.reduce((r,n)=>(r.findIndex(i=>t(n,i))===-1&&r.push(n),r),[])}function V(e){return typeof e=="string"}function xe(e){return V(e)?e:(Array.isArray(e)?e:Object.entries(e)).filter(t=>t[1]!=null)}function Rn(e){return Array.isArray(e)?e.find(t=>!Array.isArray(t)||Array.isArray(t[0]))?e.map(t=>xe(t)):[e]:[xe(e)]}function Oa(e){return e.filter(([t,r],n)=>{if(t.startsWith("$$"))return!1;for(let o=n-1;o>=0;o--)if(e[o][0]===t&&e[o][1]===r)return!1;return!0})}function ye(e){return e==null?"":Oa(e).map(([t,r])=>r!=null?`${t}:${r};`:void 0).filter(Boolean).join("")}function tt(e){return e&&typeof e=="object"&&!Array.isArray(e)}function _r(e,t,r=!1){let n=e,o=t;if(Array.isArray(o))return r&&Array.isArray(o)?[...n,...o]:[...o];let i={...n};return tt(n)&&tt(o)&&Object.keys(o).forEach(s=>{tt(n[s])&&tt(o[s])||Array.isArray(n[s])&&Array.isArray(o[s])?i[s]=_r(n[s],o[s],r):Object.assign(i,{[s]:o[s]})}),i}function Ve(e){let t,r,n;if(Array.isArray(e)){for(r=Array(t=e.length);t--;)r[t]=(n=e[t])&&typeof n=="object"?Ve(n):n;return r}if(Object.prototype.toString.call(e)==="[object Object]"){r={};for(t in e)t==="__proto__"?Object.defineProperty(r,t,{value:Ve(e[t]),configurable:!0,enumerable:!0,writable:!0}):r[t]=(n=e[t])&&typeof n=="object"?Ve(n):n;return r}return e}function Tn(e){return V(e[0])}function En(e){return V(e[0])}var Va=/[\w\u00A0-\uFFFF%-?]/;function jn(e=""){return Va.test(e)}function zn(e){return typeof e=="function"?{match:e}:e}function Pr(e){return e.length===3}function Fr(e){return e!=null}function On(){}var rt=class{_map=new Map;get(t,r){let n=this._map.get(t);if(n)return n.get(r)}getFallback(t,r,n){let o=this._map.get(t);return o||(o=new Map,this._map.set(t,o)),o.has(r)||o.set(r,n),o.get(r)}set(t,r,n){let o=this._map.get(t);return o||(o=new Map,this._map.set(t,o)),o.set(r,n),this}has(t,r){return this._map.get(t)?.has(r)}delete(t,r){return this._map.get(t)?.delete(r)||!1}deleteTop(t){return this._map.delete(t)}map(t){return Array.from(this._map.entries()).flatMap(([r,n])=>Array.from(n.entries()).map(([o,i])=>t(i,r,o)))}},nt=class extends Map{getFallback(t,r){let n=this.get(t);return n===void 0?(this.set(t,r),r):n}map(t){let r=[];return this.forEach((n,o)=>{r.push(t(n,o))}),r}flatMap(t){let r=[];return this.forEach((n,o)=>{r.push(...t(n,o))}),r}};var Ae=class extends Set{_map;constructor(t){super(t),this._map??=new Map}add(t){return this._map??=new Map,this._map.set(t,(this._map.get(t)??0)+1),super.add(t)}delete(t){return this._map.delete(t),super.delete(t)}clear(){this._map.clear(),super.clear()}getCount(t){return this._map.get(t)??0}setCount(t,r){return this._map.set(t,r),super.add(t)}};function ot(e){return e instanceof Ae}var it={};function Aa(e=["-",":"]){let t=e.join("|");return it[t]||(it[t]=new RegExp(`((?:[!@<~\\w+:_/-]|\\[&?>?:?\\S*\\])+?)(${t})\\(((?:[~!<>\\w\\s:/\\\\,%#.$?-]|\\[.*?\\])+?)\\)(?!\\s*?=>)`,"gm")),it[t].lastIndex=0,it[t]}function Ma(e,t=["-",":"],r=5){let n=Aa(t),o,i=e.toString(),s=new Set,a=new Map;do o=!1,i=i.replace(n,(u,p,f,d,m)=>{if(!t.includes(f))return u;o=!0,s.add(p+f);let $=m+p.length+f.length+1,C={length:u.length,items:[]};a.set(m,C);for(let k of[...d.matchAll(/\S+/g)]){let z=$+k.index,g=a.get(z)?.items;g?a.delete(z):g=[{offset:z,length:k[0].length,className:k[0]}];for(let w of g)w.className=w.className==="~"?p:w.className.replace(/^(!?)(.*)/,`$1${p}${f}$2`),C.items.push(w)}return"$".repeat(u.length)}),r-=1;while(o&&r);let c;if(typeof e=="string"){c="";let u=0;for(let[p,f]of a)c+=e.slice(u,p),c+=f.items.map(d=>d.className).join(" "),u=p+f.length;c+=e.slice(u)}else{c=e;for(let[u,p]of a)c.overwrite(u,u+p.length,p.items.map(f=>f.className).join(" "))}return{prefixes:Array.from(s),hasChanged:o,groupsByOffset:a,get expanded(){return c.toString()}}}function Vn(e,t=["-",":"],r=5){let n=Ma(e,t,r);return typeof e=="string"?n.expanded:e}var An=new Set;function se(e){An.has(e)||(console.warn("[unocss]",e),An.add(e))}var st=/[\\:]?[\s'"`;{}]+/g;function _a(e){return e.split(st)}var at={name:"@unocss/core/extractor-split",order:0,extract({code:e}){return _a(e)}};function Mn(){return{events:{},emit(e,...t){(this.events[e]||[]).forEach(r=>r(...t))},on(e,t){return(this.events[e]=this.events[e]||[]).push(t),()=>this.events[e]=(this.events[e]||[]).filter(r=>r!==t)}}}var Me="default",ct="preflights",Pa="shortcuts",Fa="imports",_n={[Fa]:-200,[ct]:-100,[Pa]:-10,[Me]:0};function Fn(e){return O(e).flatMap(t=>Array.isArray(t)?[t]:Object.entries(t))}var Pn="_uno_resolved";function La(e){let t=typeof e=="function"?e():e;if(Pn in t)return t;t={...t},Object.defineProperty(t,Pn,{value:!0,enumerable:!1});let r=t.shortcuts?Fn(t.shortcuts):void 0;if(t.shortcuts=r,t.prefix||t.layer){let n=o=>{o[2]||(o[2]={});let i=o[2];i.prefix==null&&t.prefix&&(i.prefix=O(t.prefix)),i.layer==null&&t.layer&&(i.layer=t.layer)};r?.forEach(n),t.rules?.forEach(n)}return t}function Ln(e){let t=La(e);if(!t.presets)return[t];let r=(t.presets||[]).flatMap(O).flatMap(Ln);return[t,...r]}function Lr(e={},t={}){let r=Object.assign({},t,e),n=Cn((r.presets||[]).flatMap(O).flatMap(Ln),(h,b)=>h.name===b.name),o=[...n.filter(h=>h.enforce==="pre"),...n.filter(h=>!h.enforce),...n.filter(h=>h.enforce==="post")],i=[...o,r],s=[...i].reverse(),a=Object.assign({},_n,...i.map(h=>h.layers));function c(h){return be(i.flatMap(b=>O(b[h]||[])))}let u=c("extractors"),p=s.find(h=>h.extractorDefault!==void 0)?.extractorDefault;p===void 0&&(p=at),p&&!u.includes(p)&&u.unshift(p),u.sort((h,b)=>(h.order||0)-(b.order||0));let f=c("rules"),d={},m=f.length,$=f.map((h,b)=>{if(Tn(h)){O(h[2]?.prefix||"").forEach(A=>{d[A+h[0]]=[b,h[1],h[2],h]});return}return[b,...h]}).filter(Boolean).reverse(),C=Ua(i.map(h=>h.theme)),k=c("extendTheme");for(let h of k)C=h(C)||C;let z={templates:be(i.flatMap(h=>O(h.autocomplete?.templates))),extractors:i.flatMap(h=>O(h.autocomplete?.extractors)).sort((h,b)=>(h.order||0)-(b.order||0)),shorthands:Wa(i.map(h=>h.autocomplete?.shorthands||{}))},g=c("separators");g.length||(g=[":","-"]);let w={mergeSelectors:!0,warn:!0,sortLayers:h=>h,...r,blocklist:c("blocklist"),presets:o,envMode:r.envMode||"build",shortcutsLayer:r.shortcutsLayer||"shortcuts",layers:a,theme:C,rulesSize:m,rulesDynamic:$,rulesStaticMap:d,preprocess:c("preprocess"),postprocess:c("postprocess"),preflights:c("preflights"),autocomplete:z,variants:c("variants").map(zn).sort((h,b)=>(h.order||0)-(b.order||0)),shortcuts:Fn(c("shortcuts")).reverse(),extractors:u,safelist:c("safelist"),separators:g,details:r.details??r.envMode==="dev"};for(let h of i)h?.configResolved?.(w);return w}function Ua(e){return e.map(t=>t?Ve(t):{}).reduce((t,r)=>_r(t,r),{})}function Wa(e){return e.reduce((t,r)=>{let n={};for(let o in r){let i=r[o];Array.isArray(i)?n[o]=`(${i.join("|")})`:n[o]=i}return{...t,...n}},{})}var Un="0.61.0";var ve={shortcutsNoMerge:"$$symbol-shortcut-no-merge",variants:"$$symbol-variants",parent:"$$symbol-parent",selector:"$$symbol-selector"},Ur=class{constructor(t={},r={}){this.userConfig=t;this.defaults=r;this.config=Lr(t,r),this.events.emit("config",this.config)}version=Un;_cache=new Map;config;blocked=new Set;parentOrders=new Map;events=Mn();setConfig(t,r){t&&(r&&(this.defaults=r),this.userConfig=t,this.blocked.clear(),this.parentOrders.clear(),this._cache.clear(),this.config=Lr(t,this.defaults),this.events.emit("config",this.config))}async applyExtractors(t,r,n=new Set){let o={original:t,code:t,id:r,extracted:n,envMode:this.config.envMode};for(let i of this.config.extractors){let s=await i.extract?.(o);if(s)if(ot(s)&&ot(n))for(let a of s)n.setCount(a,n.getCount(a)+s.getCount(a));else for(let a of s)n.add(a)}return n}makeContext(t,r){let n={rawSelector:t,currentSelector:r[1],theme:this.config.theme,generator:this,symbols:ve,variantHandlers:r[2],constructCSS:(...o)=>this.constructCustomCSS(n,...o),variantMatch:r};return n}async parseToken(t,r){if(this.blocked.has(t))return;let n=`${t}${r?` ${r}`:""}`;if(this._cache.has(n))return this._cache.get(n);let o=t;for(let u of this.config.preprocess)o=u(t);if(this.isBlocked(o)){this.blocked.add(t),this._cache.set(n,null);return}let i=await this.matchVariants(t,o);if(!i||this.isBlocked(i[1])){this.blocked.add(t),this._cache.set(n,null);return}let s=this.makeContext(t,[r||i[0],i[1],i[2],i[3]]);this.config.details&&(s.variants=[...i[3]]);let a=await this.expandShortcut(s.currentSelector,s),c=a?await this.stringifyShortcuts(s.variantMatch,s,a[0],a[1]):(await this.parseUtil(s.variantMatch,s))?.map(u=>this.stringifyUtil(u,s)).filter(Fr);if(c?.length)return this._cache.set(n,c),c;this._cache.set(n,null)}async generate(t,r={}){let{id:n,scope:o,preflights:i=!0,safelist:s=!0,minify:a=!1,extendedInfo:c=!1}=r,u=this.config.outputToCssLayers,p=V(t)?await this.applyExtractors(t,n,c?new Ae:new Set):Array.isArray(t)?new Set(t):t;if(s){let b={generator:this,theme:this.config.theme};this.config.safelist.flatMap(T=>typeof T=="function"?T(b):T).forEach(T=>{p.has(T)||p.add(T)})}let f=a?"":`
`,d=new Set([Me]),m=c?new Map:new Set,$=new Map,C={},k=Array.from(p).map(async b=>{if(m.has(b))return;let T=await this.parseToken(b);if(T!=null){m instanceof Map?m.set(b,{data:T,count:ot(p)?p.getCount(b):-1}):m.add(b);for(let A of T){let P=A[3]||"",K=A[4]?.layer;$.has(P)||$.set(P,[]),$.get(P).push(A),K&&d.add(K)}}});await Promise.all(k),await(async()=>{if(!i)return;let b={generator:this,theme:this.config.theme},T=new Set([]);this.config.preflights.forEach(({layer:A=ct})=>{d.add(A),T.add(A)}),C=Object.fromEntries(await Promise.all(Array.from(T).map(async A=>{let K=(await Promise.all(this.config.preflights.filter(re=>(re.layer||ct)===A).map(async re=>await re.getCSS(b)))).filter(Boolean).join(f);return[A,K]})))})();let z=this.config.sortLayers(Array.from(d).sort((b,T)=>(this.config.layers[b]??0)-(this.config.layers[T]??0)||b.localeCompare(T))),g={},w=(b=Me)=>{if(g[b])return g[b];let T=Array.from($).sort((P,K)=>(this.parentOrders.get(P[0])??0)-(this.parentOrders.get(K[0])??0)||P[0]?.localeCompare(K[0]||"")||0).map(([P,K])=>{let re=K.length,ge=K.filter(y=>(y[4]?.layer||Me)===b).sort((y,R)=>y[0]-R[0]||(y[4]?.sort||0)-(R[4]?.sort||0)||y[5]?.currentSelector?.localeCompare(R[5]?.currentSelector??"")||y[1]?.localeCompare(R[1]||"")||y[2]?.localeCompare(R[2]||"")||0).map(([,y,R,,D,,H])=>[[[(y&&Da(y,o))??"",D?.sort??0]],R,!!(H??D?.noMerge)]);if(!ge.length)return;let Qe=ge.reverse().map(([y,R,D],H)=>{if(!D&&this.config.mergeSelectors)for(let oe=H+1;oe<re;oe++){let Z=ge[oe];if(Z&&!Z[2]&&(y&&Z[0]||y==null&&Z[0]==null)&&Z[1]===R)return y&&Z[0]&&Z[0].push(...y),null}let ne=y?be(y.sort((oe,Z)=>oe[1]-Z[1]||oe[0]?.localeCompare(Z[0]||"")||0).map(oe=>oe[0]).filter(Boolean)):[];return ne.length?`${ne.join(`,${f}`)}{${R}}`:R}).filter(Boolean).reverse().join(f);if(!P)return Qe;let v=P.split(" $$ ");return`${v.join("{")}{${f}${Qe}${f}${"}".repeat(v.length)}`}).filter(Boolean).join(f);if(i&&(T=[C[b],T].filter(Boolean).join(f)),u&&T){let P=typeof u=="object"?u.cssLayerName?.(b):void 0;P!==null&&(P||(P=b),T=`@layer ${P}{${f}${T}${f}}`)}let A=a?"":`/* layer: ${b} */${f}`;return g[b]=T?A+T:""},h=(b=z,T)=>b.filter(A=>!T?.includes(A)).map(A=>w(A)||"").filter(Boolean).join(f);return{get css(){return h()},layers:z,matched:m,getLayers:h,getLayer:w}}async matchVariants(t,r){let n=new Set,o=[],i=r||t,s=!0,a={rawSelector:t,theme:this.config.theme,generator:this};for(;s;){s=!1;for(let c of this.config.variants){if(!c.multiPass&&n.has(c))continue;let u=await c.match(i,a);if(u){if(V(u)){if(u===i)continue;u={matcher:u}}i=u.matcher??i,o.unshift(u),n.add(c),s=!0;break}}if(!s)break;if(o.length>500)throw new Error(`Too many variants applied to "${t}"`)}return[t,i,o,n]}applyVariants(t,r=t[4],n=t[1]){let i=r.slice().sort((u,p)=>(u.order||0)-(p.order||0)).reduceRight((u,p)=>f=>{let d=p.body?.(f.entries)||f.entries,m=Array.isArray(p.parent)?p.parent:[p.parent,void 0];return(p.handle??Ka)({...f,entries:d,selector:p.selector?.(f.selector,d)||f.selector,parent:m[0]||f.parent,parentOrder:m[1]||f.parentOrder,layer:p.layer||f.layer,sort:p.sort||f.sort},u)},u=>u)({prefix:"",selector:Ia(n),pseudo:"",entries:t[2]}),{parent:s,parentOrder:a}=i;s!=null&&a!=null&&this.parentOrders.set(s,a);let c={selector:[i.prefix,i.selector,i.pseudo].join(""),entries:i.entries,parent:s,layer:i.layer,sort:i.sort,noMerge:i.noMerge};for(let u of this.config.postprocess)u(c);return c}constructCustomCSS(t,r,n){let o=xe(r);if(V(o))return o;let{selector:i,entries:s,parent:a}=this.applyVariants([0,n||t.rawSelector,o,void 0,t.variantHandlers]),c=`${i}{${ye(s)}}`;return a?`${a}{${c}}`:c}async parseUtil(t,r,n=!1,o){let[i,s,a]=V(t)?await this.matchVariants(t):t;this.config.details&&(r.rules=r.rules??[]);let c=this.config.rulesStaticMap[s];if(c&&c[1]&&(n||!c[2]?.internal)){this.config.details&&r.rules.push(c[3]);let p=c[0],f=xe(c[1]),d=c[2];return V(f)?[[p,f,d]]:[[p,i,f,d,a]]}r.variantHandlers=a;let{rulesDynamic:u}=this.config;for(let[p,f,d,m]of u){if(m?.internal&&!n)continue;let $=s;if(m?.prefix){let g=O(m.prefix);if(o){let w=O(o);if(!g.some(h=>w.includes(h)))continue}else{let w=g.find(h=>s.startsWith(h));if(w==null)continue;$=s.slice(w.length)}}let C=$.match(f);if(!C)continue;let k=await d(C,r);if(!k)continue;if(this.config.details&&r.rules.push([f,d,m]),typeof k!="string")if(Symbol.asyncIterator in k){let g=[];for await(let w of k)w&&g.push(w);k=g}else Symbol.iterator in k&&!Array.isArray(k)&&(k=Array.from(k).filter(Fr));let z=Rn(k).filter(g=>g.length);if(z.length)return z.map(g=>{if(V(g))return[p,g,m];let w=a;for(let h of g)h[0]===ve.variants?w=[...O(h[1]),...w]:h[0]===ve.parent?w=[{parent:h[1]},...w]:h[0]===ve.selector&&(w=[{selector:h[1]},...w]);return[p,i,g,m,w]})}}stringifyUtil(t,r){if(!t)return;if(Pr(t))return[t[0],void 0,t[1],void 0,t[2],this.config.details?r:void 0,void 0];let{selector:n,entries:o,parent:i,layer:s,sort:a,noMerge:c}=this.applyVariants(t),u=ye(o);if(!u)return;let{layer:p,sort:f,...d}=t[3]??{},m={...d,layer:s??p,sort:a??f};return[t[0],n,u,i,m,this.config.details?r:void 0,c]}async expandShortcut(t,r,n=5){if(n===0)return;let o=this.config.details?a=>{r.shortcuts=r.shortcuts??[],r.shortcuts.push(a)}:On,i,s;for(let a of this.config.shortcuts){let c=t;if(a[2]?.prefix){let p=O(a[2].prefix).find(f=>t.startsWith(f));if(p==null)continue;c=t.slice(p.length)}if(En(a)){if(a[0]===c){i=i||a[2],s=a[1],o(a);break}}else{let u=c.match(a[0]);if(u&&(s=a[1](u,r)),s){i=i||a[2],o(a);break}}}if(V(s)&&(s=Vn(s.trim()).split(/\s+/g)),!s){let[a,c]=V(t)?await this.matchVariants(t):t;if(a!==c){let u=await this.expandShortcut(c,r,n-1);u&&(s=u[0].map(p=>V(p)?a.replace(c,p):p))}}if(s)return[(await Promise.all(s.map(async a=>(V(a)?(await this.expandShortcut(a,r,n-1))?.[0]:void 0)||[a]))).flat(1).filter(Boolean),i]}async stringifyShortcuts(t,r,n,o={layer:this.config.shortcutsLayer}){let i=new nt,s=(await Promise.all(be(n).map(async p=>{let f=V(p)?await this.parseUtil(p,r,!0,o.prefix):[[Number.POSITIVE_INFINITY,"{inline}",xe(p),void 0,[]]];return!f&&this.config.warn&&se(`unmatched utility "${p}" in shortcut "${t[1]}"`),f||[]}))).flat(1).filter(Boolean).sort((p,f)=>p[0]-f[0]),[a,,c]=t,u=[];for(let p of s){if(Pr(p)){u.push([p[0],void 0,p[1],void 0,p[2],r,void 0]);continue}let{selector:f,entries:d,parent:m,sort:$,noMerge:C,layer:k}=this.applyVariants(p,[...p[4],...c],a);i.getFallback(k??o.layer,new rt).getFallback(f,m,[[],p[0]])[0].push([d,!!(C??p[3]?.noMerge),$??0])}return u.concat(i.flatMap((p,f)=>p.map(([d,m],$,C)=>{let k=(g,w,h)=>{let b=Math.max(...h.map(A=>A[1])),T=h.map(A=>A[0]);return(g?[T.flat(1)]:T).map(A=>{let P=ye(A);if(P)return[m,$,P,C,{...o,noMerge:w,sort:b,layer:f},r,void 0]})};return[[d.filter(([,g])=>g).map(([g,,w])=>[g,w]),!0],[d.filter(([,g])=>!g).map(([g,,w])=>[g,w]),!1]].map(([g,w])=>[...k(!1,w,g.filter(([h])=>h.some(b=>b[0]===ve.shortcutsNoMerge))),...k(!0,w,g.filter(([h])=>h.every(b=>b[0]!==ve.shortcutsNoMerge)))])}).flat(2).filter(Boolean)))}isBlocked(t){return!t||this.config.blocklist.map(r=>Array.isArray(r)?r[0]:r).some(r=>typeof r=="function"?r(t):V(r)?r===t:r.test(t))}getBlocked(t){let r=this.config.blocklist.find(n=>{let o=Array.isArray(n)?n[0]:n;return typeof o=="function"?o(t):V(o)?o===t:o.test(t)});return r?Array.isArray(r)?r:[r,void 0]:void 0}};function Nn(e,t){return new Ur(e,t)}var Bn=/\s\$\$\s+/g;function Ba(e){return Bn.test(e)}function Da(e,t){return Ba(e)?e.replace(Bn,t?` ${t} `:" "):t?`${t} ${e}`:e}var Wn=/^\[(.+?)(~?=)"(.*)"\]$/;function Ia(e){return Wn.test(e)?e.replace(Wn,(t,r,n,o)=>`[${et(r)}${n}"${et(o)}"]`):`.${et(e)}`}function Ka(e,t){return t(e)}function Ha(e){let t,r,n=2166136261;for(t=0,r=e.length;t<r;t++)n^=e.charCodeAt(t),n+=(n<<1)+(n<<4)+(n<<7)+(n<<8)+(n<<24);return`00000${(n>>>0).toString(36)}`.slice(-6)}function Dn(e,t,r,n){for(let o of Array.from(e.matchAll(r)))if(o!=null){let i=o[0],s=`${n}${Ha(i)}`;t.set(s,i),e=e.replace(i,s)}return e}function In(e,t){for(let[r,n]of t.entries())e=e.replaceAll(r,n);return e}var Ga=/\/\/#\s*sourceMappingURL=.*\n?/g;function Kn(e){return e.includes("sourceMappingURL=")?e.replace(Ga,""):e}var qa=/(?:[\w&:[\]-]|\[\S{1,64}=\S{1,64}\]){1,64}\[\\?['"]?\S{1,64}?['"]\]\]?[\w:-]{0,64}/g,Ya=/\[(\\\W|[\w-]){1,64}:[^\s:]{0,64}?("\S{1,64}?"|'\S{1,64}?'|`\S{1,64}?`|[^\s:]{1,64}?)[^\s:]{0,64}?\)?\]/g,Xa=/^\[(?:\\\W|[\w-]){1,64}:['"]?\S{1,64}?['"]?\]$/;function Za(e){let t=[];for(let o of e.matchAll(Ya))o.index!==0&&!/^[\s'"`]/.test(e[o.index-1]??"")||t.push(o[0]);for(let o of e.matchAll(qa))t.push(o[0]);let r=new Map,n="@unocss-skip-arbitrary-brackets";return e=Dn(e,r,/-\[[^\]]*\]/g,n),e&&e.split(st).forEach(o=>{o.includes(n)&&(o=In(o,r)),jn(o)&&!Xa.test(o)&&t.push(o)}),t}var Hn={name:"@unocss/extractor-arbitrary-variants",order:0,extract({code:e}){return Za(Kn(e))}};var Gn=[{layer:"preflights",getCSS(e){if(e.theme.preflightBase){let t=ye(Object.entries(e.theme.preflightBase));return O(e.theme.preflightRoot??["*,::before,::after","::backdrop"]).map(n=>`${n}{${t}}`).join("")}}}];var U={l:["-left"],r:["-right"],t:["-top"],b:["-bottom"],s:["-inline-start"],e:["-inline-end"],x:["-left","-right"],y:["-top","-bottom"],"":[""],bs:["-block-start"],be:["-block-end"],is:["-inline-start"],ie:["-inline-end"],block:["-block-start","-block-end"],inline:["-inline-start","-inline-end"]},Wr={...U,s:["-inset-inline-start"],start:["-inset-inline-start"],e:["-inset-inline-end"],end:["-inset-inline-end"],bs:["-inset-block-start"],be:["-inset-block-end"],is:["-inset-inline-start"],ie:["-inset-inline-end"],block:["-inset-block-start","-inset-block-end"],inline:["-inset-inline-start","-inset-inline-end"]},Nr={l:["-top-left","-bottom-left"],r:["-top-right","-bottom-right"],t:["-top-left","-top-right"],b:["-bottom-left","-bottom-right"],tl:["-top-left"],lt:["-top-left"],tr:["-top-right"],rt:["-top-right"],bl:["-bottom-left"],lb:["-bottom-left"],br:["-bottom-right"],rb:["-bottom-right"],"":[""],bs:["-start-start","-start-end"],be:["-end-start","-end-end"],s:["-end-start","-start-start"],is:["-end-start","-start-start"],e:["-start-end","-end-end"],ie:["-start-end","-end-end"],ss:["-start-start"],"bs-is":["-start-start"],"is-bs":["-start-start"],se:["-start-end"],"bs-ie":["-start-end"],"ie-bs":["-start-end"],es:["-end-start"],"be-is":["-end-start"],"is-be":["-end-start"],ee:["-end-end"],"be-ie":["-end-end"],"ie-be":["-end-end"]},Yn={x:["-x"],y:["-y"],z:["-z"],"":["-x","-y"]},Xn=["x","y","z"],qn=["top","top center","top left","top right","bottom","bottom center","bottom left","bottom right","left","left center","left top","left bottom","right","right center","right top","right bottom","center","center top","center bottom","center left","center right","center center"],L=Object.assign({},...qn.map(e=>({[e.replace(/ /,"-")]:e})),...qn.map(e=>({[e.replace(/\b(\w)\w+/g,"$1").replace(/ /,"")]:e}))),S=["inherit","initial","revert","revert-layer","unset"],_e=/^(calc|clamp|min|max)\s*\((.+)\)(.*)/,lt=/^(var)\s*\((.+)\)(.*)/;function we(e,t,r){if(e==="")return;let n=e.length,o=0,i=!1,s=0;for(let a=0;a<n;a++)switch(e[a]){case t:i||(i=!0,s=a),o++;break;case r:if(--o,o<0)return;if(o===0)return[e.slice(s,a+1),e.slice(a+1),e.slice(0,s)];break}}function ae(e,t,r,n){if(e===""||(V(n)&&(n=[n]),n.length===0))return;let o=e.length,i=0;for(let s=0;s<o;s++)switch(e[s]){case t:i++;break;case r:if(--i<0)return;break;default:for(let a of n){let c=a.length;if(c&&a===e.slice(s,s+c)&&i===0)return s===0||s===o-c?void 0:[e.slice(0,s),e.slice(s+c)]}}return[e,""]}function fe(e,t,r){r=r??10;let n=[],o=0;for(;e!=="";){if(++o>r)return;let i=ae(e,"(",")",t);if(!i)return;let[s,a]=i;n.push(s),e=a}if(n.length>0)return n}var Br=["hsl","hsla","hwb","lab","lch","oklab","oklch","rgb","rgba"],Zn=["%alpha","<alpha-value>"],Ja=new RegExp(Zn.map(e=>ie(e)).join("|"));function I(e=""){let t=Qa(e);if(t==null||t===!1)return;let{type:r,components:n,alpha:o}=t,i=r.toLowerCase();if(n.length!==0&&!(Br.includes(i)&&![1,3].includes(n.length)))return{type:i,components:n.map(s=>typeof s=="string"?s.trim():s),alpha:typeof o=="string"?o.trim():o}}function ee(e){let t=e.alpha??1;return typeof t=="string"&&Zn.includes(t)?1:t}function M(e,t){if(typeof e=="string")return e.replace(Ja,`${t??1}`);let{components:r}=e,{alpha:n,type:o}=e;return n=t??n,o=o.toLowerCase(),["hsla","rgba"].includes(o)?`${o}(${r.join(", ")}${n==null?"":`, ${n}`})`:(n=n==null?"":` / ${n}`,Br.includes(o)?`${o}(${r.join(" ")}${n})`:`color(${o} ${r.join(" ")}${n})`)}function Qa(e){if(!e)return;let t=ec(e);if(t!=null||(t=tc(e),t!=null)||(t=rc(e),t!=null)||(t=oc(e),t!=null)||(t=ic(e),t!=null))return t}function ec(e){let[,t]=e.match(/^#([\da-f]+)$/i)||[];if(t)switch(t.length){case 3:case 4:let r=Array.from(t,o=>Number.parseInt(o,16)).map(o=>o<<4|o);return{type:"rgb",components:r.slice(0,3),alpha:t.length===3?void 0:Math.round(r[3]/255*100)/100};case 6:case 8:let n=Number.parseInt(t,16);return{type:"rgb",components:t.length===6?[n>>16&255,n>>8&255,n&255]:[n>>24&255,n>>16&255,n>>8&255],alpha:t.length===6?void 0:Math.round((n&255)/255*100)/100}}}function tc(e){let t={rebeccapurple:[102,51,153,1]}[e];if(t!=null)return{type:"rgb",components:t.slice(0,3),alpha:t[3]}}function rc(e){let t=e.match(/^(rgb|rgba|hsl|hsla)\((.+)\)$/i);if(!t)return;let[,r,n]=t,o=fe(n,",",5);if(o){if([3,4].includes(o.length))return{type:r,components:o.slice(0,3),alpha:o[3]};if(o.length!==1)return!1}}var nc=new RegExp(`^(${Br.join("|")})\\((.+)\\)$`,"i");function oc(e){let t=e.match(nc);if(!t)return;let[,r,n]=t,o=Jn(`${r} ${n}`);if(o){let{alpha:i,components:[s,...a]}=o;return{type:s,components:a,alpha:i}}}function ic(e){let t=e.match(/^color\((.+)\)$/);if(!t)return;let r=Jn(t[1]);if(r){let{alpha:n,components:[o,...i]}=r;return{type:o,components:i,alpha:n}}}function Jn(e){let t=fe(e," ");if(!t)return;let r=t.length;if(t[r-2]==="/")return{components:t.slice(0,r-2),alpha:t[r-1]};if(t[r-2]!=null&&(t[r-2].endsWith("/")||t[r-1].startsWith("/"))){let i=t.splice(r-2);t.push(i.join(" ")),--r}let n=fe(t[r-1],"/",2);if(!n)return;if(n.length===1||n[n.length-1]==="")return{components:t};let o=n.pop();return t[r-1]=n.join("/"),{components:t,alpha:o}}function ut(e){let t=function(n){let o=this.__options?.sequence||[];this.__options.sequence=[];for(let i of o){let s=e[i](n);if(s!=null)return s}};function r(n,o){return n.__options||(n.__options={sequence:[]}),n.__options.sequence.push(o),n}for(let n of Object.keys(e))Object.defineProperty(t,n,{enumerable:!0,configurable:!0,get(){return r(this,n)}});return t}function N(e,t){let r;return{name:e,match(n,o){r||(r=new RegExp(`^${ie(e)}(?:${o.generator.config.separators.join("|")})`));let i=n.match(r);if(i)return{matcher:n.slice(i[0].length),handle:(s,a)=>a({...s,...t(s)})}},autocomplete:`${e}:`}}function B(e,t){let r;return{name:e,match(n,o){r||(r=new RegExp(`^${ie(e)}(?:${o.generator.config.separators.join("|")})`));let i=n.match(r);if(i)return{matcher:n.slice(i[0].length),handle:(s,a)=>a({...s,parent:`${s.parent?`${s.parent} $$ `:""}${t}`})}},autocomplete:`${e}:`}}function ce(e,t,r){if(t.startsWith(`${e}[`)){let[n,o]=we(t.slice(e.length),"[","]")??[];if(n&&o){for(let i of r)if(o.startsWith(i))return[n,o.slice(i.length),i];return[n,o,""]}}}function W(e,t,r){if(t.startsWith(e)){let n=ce(e,t,r);if(n){let[o="",i=n[1]]=W("/",n[1],r)??[];return[n[0],i,o]}for(let o of r.filter(i=>i!=="/")){let i=t.indexOf(o,e.length);if(i!==-1){let s=t.indexOf("/",e.length),a=s===-1||i<=s;return[t.slice(e.length,a?i:s),t.slice(i+o.length),a?"":t.slice(s+1,i)]}}}}var Qn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",eo=new Uint8Array(64),sc=new Uint8Array(128);for(let e=0;e<Qn.length;e++){let t=Qn.charCodeAt(e);eo[e]=t,sc[t]=e}var Dr=typeof TextDecoder<"u"?new TextDecoder:typeof Buffer<"u"?{decode(e){return Buffer.from(e.buffer,e.byteOffset,e.byteLength).toString()}}:{decode(e){let t="";for(let r=0;r<e.length;r++)t+=String.fromCharCode(e[r]);return t}};function to(e){let t=new Int32Array(5),r=1024*16,n=r-36,o=new Uint8Array(r),i=o.subarray(0,n),s=0,a="";for(let c=0;c<e.length;c++){let u=e[c];if(c>0&&(s===r&&(a+=Dr.decode(o),s=0),o[s++]=59),u.length!==0){t[0]=0;for(let p=0;p<u.length;p++){let f=u[p];s>n&&(a+=Dr.decode(i),o.copyWithin(0,n,s),s-=n),p>0&&(o[s++]=44),s=Pe(o,s,t,f,0),f.length!==1&&(s=Pe(o,s,t,f,1),s=Pe(o,s,t,f,2),s=Pe(o,s,t,f,3),f.length!==4&&(s=Pe(o,s,t,f,4)))}}}return a+Dr.decode(o.subarray(0,s))}function Pe(e,t,r,n,o){let i=n[o],s=i-r[o];r[o]=i,s=s<0?-s<<1|1:s<<1;do{let a=s&31;s>>>=5,s>0&&(a|=32),e[t++]=eo[a]}while(s>0);return t}var ft=class e{constructor(t){this.bits=t instanceof e?t.bits.slice():[]}add(t){this.bits[t>>5]|=1<<(t&31)}has(t){return!!(this.bits[t>>5]&1<<(t&31))}},pt=class e{constructor(t,r,n){this.start=t,this.end=r,this.original=n,this.intro="",this.outro="",this.content=n,this.storeName=!1,this.edited=!1,this.previous=null,this.next=null}appendLeft(t){this.outro+=t}appendRight(t){this.intro=this.intro+t}clone(){let t=new e(this.start,this.end,this.original);return t.intro=this.intro,t.outro=this.outro,t.content=this.content,t.storeName=this.storeName,t.edited=this.edited,t}contains(t){return this.start<t&&t<this.end}eachNext(t){let r=this;for(;r;)t(r),r=r.next}eachPrevious(t){let r=this;for(;r;)t(r),r=r.previous}edit(t,r,n){return this.content=t,n||(this.intro="",this.outro=""),this.storeName=r,this.edited=!0,this}prependLeft(t){this.outro=t+this.outro}prependRight(t){this.intro=t+this.intro}reset(){this.intro="",this.outro="",this.edited&&(this.content=this.original,this.storeName=!1,this.edited=!1)}split(t){let r=t-this.start,n=this.original.slice(0,r),o=this.original.slice(r);this.original=n;let i=new e(t,this.end,o);return i.outro=this.outro,this.outro="",this.end=t,this.edited?(i.edit("",!1),this.content=""):this.content=n,i.next=this.next,i.next&&(i.next.previous=i),i.previous=this,this.next=i,i}toString(){return this.intro+this.content+this.outro}trimEnd(t){if(this.outro=this.outro.replace(t,""),this.outro.length)return!0;let r=this.content.replace(t,"");if(r.length)return r!==this.content&&(this.split(this.start+r.length).edit("",void 0,!0),this.edited&&this.edit(r,this.storeName,!0)),!0;if(this.edit("",void 0,!0),this.intro=this.intro.replace(t,""),this.intro.length)return!0}trimStart(t){if(this.intro=this.intro.replace(t,""),this.intro.length)return!0;let r=this.content.replace(t,"");if(r.length){if(r!==this.content){let n=this.split(this.end-r.length);this.edited&&n.edit(r,this.storeName,!0),this.edit("",void 0,!0)}return!0}else if(this.edit("",void 0,!0),this.outro=this.outro.replace(t,""),this.outro.length)return!0}};function ac(){return typeof globalThis<"u"&&typeof globalThis.btoa=="function"?e=>globalThis.btoa(unescape(encodeURIComponent(e))):typeof Buffer=="function"?e=>Buffer.from(e,"utf-8").toString("base64"):()=>{throw new Error("Unsupported environment: `window.btoa` or `Buffer` should be supported.")}}var cc=ac(),Ir=class{constructor(t){this.version=3,this.file=t.file,this.sources=t.sources,this.sourcesContent=t.sourcesContent,this.names=t.names,this.mappings=to(t.mappings),typeof t.x_google_ignoreList<"u"&&(this.x_google_ignoreList=t.x_google_ignoreList)}toString(){return JSON.stringify(this)}toUrl(){return"data:application/json;charset=utf-8;base64,"+cc(this.toString())}};function lc(e){let t=e.split(`
`),r=t.filter(i=>/^\t+/.test(i)),n=t.filter(i=>/^ {2,}/.test(i));if(r.length===0&&n.length===0)return null;if(r.length>=n.length)return"	";let o=n.reduce((i,s)=>{let a=/^ +/.exec(s)[0].length;return Math.min(a,i)},1/0);return new Array(o+1).join(" ")}function uc(e,t){let r=e.split(/[/\\]/),n=t.split(/[/\\]/);for(r.pop();r[0]===n[0];)r.shift(),n.shift();if(r.length){let o=r.length;for(;o--;)r[o]=".."}return r.concat(n).join("/")}var fc=Object.prototype.toString;function pc(e){return fc.call(e)==="[object Object]"}function ro(e){let t=e.split(`
`),r=[];for(let n=0,o=0;n<t.length;n++)r.push(o),o+=t[n].length+1;return function(o){let i=0,s=r.length;for(;i<s;){let u=i+s>>1;o<r[u]?s=u:i=u+1}let a=i-1,c=o-r[a];return{line:a,column:c}}}var dc=/\w/,Kr=class{constructor(t){this.hires=t,this.generatedCodeLine=0,this.generatedCodeColumn=0,this.raw=[],this.rawSegments=this.raw[this.generatedCodeLine]=[],this.pending=null}addEdit(t,r,n,o){if(r.length){let i=r.length-1,s=r.indexOf(`
`,0),a=-1;for(;s>=0&&i>s;){let u=[this.generatedCodeColumn,t,n.line,n.column];o>=0&&u.push(o),this.rawSegments.push(u),this.generatedCodeLine+=1,this.raw[this.generatedCodeLine]=this.rawSegments=[],this.generatedCodeColumn=0,a=s,s=r.indexOf(`
`,s+1)}let c=[this.generatedCodeColumn,t,n.line,n.column];o>=0&&c.push(o),this.rawSegments.push(c),this.advance(r.slice(a+1))}else this.pending&&(this.rawSegments.push(this.pending),this.advance(r));this.pending=null}addUneditedChunk(t,r,n,o,i){let s=r.start,a=!0,c=!1;for(;s<r.end;){if(this.hires||a||i.has(s)){let u=[this.generatedCodeColumn,t,o.line,o.column];this.hires==="boundary"?dc.test(n[s])?c||(this.rawSegments.push(u),c=!0):(this.rawSegments.push(u),c=!1):this.rawSegments.push(u)}n[s]===`
`?(o.line+=1,o.column=0,this.generatedCodeLine+=1,this.raw[this.generatedCodeLine]=this.rawSegments=[],this.generatedCodeColumn=0,a=!0):(o.column+=1,this.generatedCodeColumn+=1,a=!1),s+=1}this.pending=null}advance(t){if(!t)return;let r=t.split(`
`);if(r.length>1){for(let n=0;n<r.length-1;n++)this.generatedCodeLine++,this.raw[this.generatedCodeLine]=this.rawSegments=[];this.generatedCodeColumn=0}this.generatedCodeColumn+=r[r.length-1].length}},Fe=`
`,$e={insertLeft:!1,insertRight:!1,storeName:!1},dt=class e{constructor(t,r={}){let n=new pt(0,t.length,t);Object.defineProperties(this,{original:{writable:!0,value:t},outro:{writable:!0,value:""},intro:{writable:!0,value:""},firstChunk:{writable:!0,value:n},lastChunk:{writable:!0,value:n},lastSearchedChunk:{writable:!0,value:n},byStart:{writable:!0,value:{}},byEnd:{writable:!0,value:{}},filename:{writable:!0,value:r.filename},indentExclusionRanges:{writable:!0,value:r.indentExclusionRanges},sourcemapLocations:{writable:!0,value:new ft},storedNames:{writable:!0,value:{}},indentStr:{writable:!0,value:void 0},ignoreList:{writable:!0,value:r.ignoreList}}),this.byStart[0]=n,this.byEnd[t.length]=n}addSourcemapLocation(t){this.sourcemapLocations.add(t)}append(t){if(typeof t!="string")throw new TypeError("outro content must be a string");return this.outro+=t,this}appendLeft(t,r){if(typeof r!="string")throw new TypeError("inserted content must be a string");this._split(t);let n=this.byEnd[t];return n?n.appendLeft(r):this.intro+=r,this}appendRight(t,r){if(typeof r!="string")throw new TypeError("inserted content must be a string");this._split(t);let n=this.byStart[t];return n?n.appendRight(r):this.outro+=r,this}clone(){let t=new e(this.original,{filename:this.filename}),r=this.firstChunk,n=t.firstChunk=t.lastSearchedChunk=r.clone();for(;r;){t.byStart[n.start]=n,t.byEnd[n.end]=n;let o=r.next,i=o&&o.clone();i&&(n.next=i,i.previous=n,n=i),r=o}return t.lastChunk=n,this.indentExclusionRanges&&(t.indentExclusionRanges=this.indentExclusionRanges.slice()),t.sourcemapLocations=new ft(this.sourcemapLocations),t.intro=this.intro,t.outro=this.outro,t}generateDecodedMap(t){t=t||{};let r=0,n=Object.keys(this.storedNames),o=new Kr(t.hires),i=ro(this.original);return this.intro&&o.advance(this.intro),this.firstChunk.eachNext(s=>{let a=i(s.start);s.intro.length&&o.advance(s.intro),s.edited?o.addEdit(r,s.content,a,s.storeName?n.indexOf(s.original):-1):o.addUneditedChunk(r,s,this.original,a,this.sourcemapLocations),s.outro.length&&o.advance(s.outro)}),{file:t.file?t.file.split(/[/\\]/).pop():void 0,sources:[t.source?uc(t.file||"",t.source):t.file||""],sourcesContent:t.includeContent?[this.original]:void 0,names:n,mappings:o.raw,x_google_ignoreList:this.ignoreList?[r]:void 0}}generateMap(t){return new Ir(this.generateDecodedMap(t))}_ensureindentStr(){this.indentStr===void 0&&(this.indentStr=lc(this.original))}_getRawIndentString(){return this._ensureindentStr(),this.indentStr}getIndentString(){return this._ensureindentStr(),this.indentStr===null?"	":this.indentStr}indent(t,r){let n=/^[^\r\n]/gm;if(pc(t)&&(r=t,t=void 0),t===void 0&&(this._ensureindentStr(),t=this.indentStr||"	"),t==="")return this;r=r||{};let o={};r.exclude&&(typeof r.exclude[0]=="number"?[r.exclude]:r.exclude).forEach(p=>{for(let f=p[0];f<p[1];f+=1)o[f]=!0});let i=r.indentStart!==!1,s=u=>i?`${t}${u}`:(i=!0,u);this.intro=this.intro.replace(n,s);let a=0,c=this.firstChunk;for(;c;){let u=c.end;if(c.edited)o[a]||(c.content=c.content.replace(n,s),c.content.length&&(i=c.content[c.content.length-1]===`
`));else for(a=c.start;a<u;){if(!o[a]){let p=this.original[a];p===`
`?i=!0:p!=="\r"&&i&&(i=!1,a===c.start||(this._splitChunk(c,a),c=c.next),c.prependRight(t))}a+=1}a=c.end,c=c.next}return this.outro=this.outro.replace(n,s),this}insert(){throw new Error("magicString.insert(...) is deprecated. Use prependRight(...) or appendLeft(...)")}insertLeft(t,r){return $e.insertLeft||(console.warn("magicString.insertLeft(...) is deprecated. Use magicString.appendLeft(...) instead"),$e.insertLeft=!0),this.appendLeft(t,r)}insertRight(t,r){return $e.insertRight||(console.warn("magicString.insertRight(...) is deprecated. Use magicString.prependRight(...) instead"),$e.insertRight=!0),this.prependRight(t,r)}move(t,r,n){if(n>=t&&n<=r)throw new Error("Cannot move a selection inside itself");this._split(t),this._split(r),this._split(n);let o=this.byStart[t],i=this.byEnd[r],s=o.previous,a=i.next,c=this.byStart[n];if(!c&&i===this.lastChunk)return this;let u=c?c.previous:this.lastChunk;return s&&(s.next=a),a&&(a.previous=s),u&&(u.next=o),c&&(c.previous=i),o.previous||(this.firstChunk=i.next),i.next||(this.lastChunk=o.previous,this.lastChunk.next=null),o.previous=u,i.next=c||null,u||(this.firstChunk=o),c||(this.lastChunk=i),this}overwrite(t,r,n,o){return o=o||{},this.update(t,r,n,{...o,overwrite:!o.contentOnly})}update(t,r,n,o){if(typeof n!="string")throw new TypeError("replacement content must be a string");for(;t<0;)t+=this.original.length;for(;r<0;)r+=this.original.length;if(r>this.original.length)throw new Error("end is out of bounds");if(t===r)throw new Error("Cannot overwrite a zero-length range \u2013 use appendLeft or prependRight instead");this._split(t),this._split(r),o===!0&&($e.storeName||(console.warn("The final argument to magicString.overwrite(...) should be an options object. See https://github.com/rich-harris/magic-string"),$e.storeName=!0),o={storeName:!0});let i=o!==void 0?o.storeName:!1,s=o!==void 0?o.overwrite:!1;if(i){let u=this.original.slice(t,r);Object.defineProperty(this.storedNames,u,{writable:!0,value:!0,enumerable:!0})}let a=this.byStart[t],c=this.byEnd[r];if(a){let u=a;for(;u!==c;){if(u.next!==this.byStart[u.end])throw new Error("Cannot overwrite across a split point");u=u.next,u.edit("",!1)}a.edit(n,i,!s)}else{let u=new pt(t,r,"").edit(n,i);c.next=u,u.previous=c}return this}prepend(t){if(typeof t!="string")throw new TypeError("outro content must be a string");return this.intro=t+this.intro,this}prependLeft(t,r){if(typeof r!="string")throw new TypeError("inserted content must be a string");this._split(t);let n=this.byEnd[t];return n?n.prependLeft(r):this.intro=r+this.intro,this}prependRight(t,r){if(typeof r!="string")throw new TypeError("inserted content must be a string");this._split(t);let n=this.byStart[t];return n?n.prependRight(r):this.outro=r+this.outro,this}remove(t,r){for(;t<0;)t+=this.original.length;for(;r<0;)r+=this.original.length;if(t===r)return this;if(t<0||r>this.original.length)throw new Error("Character is out of bounds");if(t>r)throw new Error("end must be greater than start");this._split(t),this._split(r);let n=this.byStart[t];for(;n;)n.intro="",n.outro="",n.edit(""),n=r>n.end?this.byStart[n.end]:null;return this}reset(t,r){for(;t<0;)t+=this.original.length;for(;r<0;)r+=this.original.length;if(t===r)return this;if(t<0||r>this.original.length)throw new Error("Character is out of bounds");if(t>r)throw new Error("end must be greater than start");this._split(t),this._split(r);let n=this.byStart[t];for(;n;)n.reset(),n=r>n.end?this.byStart[n.end]:null;return this}lastChar(){if(this.outro.length)return this.outro[this.outro.length-1];let t=this.lastChunk;do{if(t.outro.length)return t.outro[t.outro.length-1];if(t.content.length)return t.content[t.content.length-1];if(t.intro.length)return t.intro[t.intro.length-1]}while(t=t.previous);return this.intro.length?this.intro[this.intro.length-1]:""}lastLine(){let t=this.outro.lastIndexOf(Fe);if(t!==-1)return this.outro.substr(t+1);let r=this.outro,n=this.lastChunk;do{if(n.outro.length>0){if(t=n.outro.lastIndexOf(Fe),t!==-1)return n.outro.substr(t+1)+r;r=n.outro+r}if(n.content.length>0){if(t=n.content.lastIndexOf(Fe),t!==-1)return n.content.substr(t+1)+r;r=n.content+r}if(n.intro.length>0){if(t=n.intro.lastIndexOf(Fe),t!==-1)return n.intro.substr(t+1)+r;r=n.intro+r}}while(n=n.previous);return t=this.intro.lastIndexOf(Fe),t!==-1?this.intro.substr(t+1)+r:this.intro+r}slice(t=0,r=this.original.length){for(;t<0;)t+=this.original.length;for(;r<0;)r+=this.original.length;let n="",o=this.firstChunk;for(;o&&(o.start>t||o.end<=t);){if(o.start<r&&o.end>=r)return n;o=o.next}if(o&&o.edited&&o.start!==t)throw new Error(`Cannot use replaced character ${t} as slice start anchor.`);let i=o;for(;o;){o.intro&&(i!==o||o.start===t)&&(n+=o.intro);let s=o.start<r&&o.end>=r;if(s&&o.edited&&o.end!==r)throw new Error(`Cannot use replaced character ${r} as slice end anchor.`);let a=i===o?t-o.start:0,c=s?o.content.length+r-o.end:o.content.length;if(n+=o.content.slice(a,c),o.outro&&(!s||o.end===r)&&(n+=o.outro),s)break;o=o.next}return n}snip(t,r){let n=this.clone();return n.remove(0,t),n.remove(r,n.original.length),n}_split(t){if(this.byStart[t]||this.byEnd[t])return;let r=this.lastSearchedChunk,n=t>r.end;for(;r;){if(r.contains(t))return this._splitChunk(r,t);r=n?this.byStart[r.end]:this.byEnd[r.start]}}_splitChunk(t,r){if(t.edited&&t.content.length){let o=ro(this.original)(r);throw new Error(`Cannot split a chunk that has already been edited (${o.line}:${o.column} \u2013 "${t.original}")`)}let n=t.split(r);return this.byEnd[r]=t,this.byStart[r]=n,this.byEnd[n.end]=n,t===this.lastChunk&&(this.lastChunk=n),this.lastSearchedChunk=t,!0}toString(){let t=this.intro,r=this.firstChunk;for(;r;)t+=r.toString(),r=r.next;return t+this.outro}isEmpty(){let t=this.firstChunk;do if(t.intro.length&&t.intro.trim()||t.content.length&&t.content.trim()||t.outro.length&&t.outro.trim())return!1;while(t=t.next);return!0}length(){let t=this.firstChunk,r=0;do r+=t.intro.length+t.content.length+t.outro.length;while(t=t.next);return r}trimLines(){return this.trim("[\\r\\n]")}trim(t){return this.trimStart(t).trimEnd(t)}trimEndAborted(t){let r=new RegExp((t||"\\s")+"+$");if(this.outro=this.outro.replace(r,""),this.outro.length)return!0;let n=this.lastChunk;do{let o=n.end,i=n.trimEnd(r);if(n.end!==o&&(this.lastChunk===n&&(this.lastChunk=n.next),this.byEnd[n.end]=n,this.byStart[n.next.start]=n.next,this.byEnd[n.next.end]=n.next),i)return!0;n=n.previous}while(n);return!1}trimEnd(t){return this.trimEndAborted(t),this}trimStartAborted(t){let r=new RegExp("^"+(t||"\\s")+"+");if(this.intro=this.intro.replace(r,""),this.intro.length)return!0;let n=this.firstChunk;do{let o=n.end,i=n.trimStart(r);if(n.end!==o&&(n===this.lastChunk&&(this.lastChunk=n.next),this.byEnd[n.end]=n,this.byStart[n.next.start]=n.next,this.byEnd[n.next.end]=n.next),i)return!0;n=n.next}while(n);return!1}trimStart(t){return this.trimStartAborted(t),this}hasChanged(){return this.original!==this.toString()}_replaceRegexp(t,r){function n(i,s){return typeof r=="string"?r.replace(/\$(\$|&|\d+)/g,(a,c)=>c==="$"?"$":c==="&"?i[0]:+c<i.length?i[+c]:`$${c}`):r(...i,i.index,s,i.groups)}function o(i,s){let a,c=[];for(;a=i.exec(s);)c.push(a);return c}if(t.global)o(t,this.original).forEach(s=>{if(s.index!=null){let a=n(s,this.original);a!==s[0]&&this.overwrite(s.index,s.index+s[0].length,a)}});else{let i=this.original.match(t);if(i&&i.index!=null){let s=n(i,this.original);s!==i[0]&&this.overwrite(i.index,i.index+i[0].length,s)}}return this}_replaceString(t,r){let{original:n}=this,o=n.indexOf(t);return o!==-1&&this.overwrite(o,o+t.length,r),this}replace(t,r){return typeof t=="string"?this._replaceString(t,r):this._replaceRegexp(t,r)}_replaceAllString(t,r){let{original:n}=this,o=t.length;for(let i=n.indexOf(t);i!==-1;i=n.indexOf(t,i+o))n.slice(i,i+o)!==r&&this.overwrite(i,i+o,r);return this}replaceAll(t,r){if(typeof t=="string")return this._replaceAllString(t,r);if(!t.global)throw new TypeError("MagicString.prototype.replaceAll called with a non-global RegExp argument");return this._replaceRegexp(t,r)}};var mc=/theme\(\s*(['"])?([^\1]*?)\1?\s*\)/g;function no(e){return e.includes("theme(")&&e.includes(")")}function oo(e,t,r=!0){let n=Array.from(e.toString().matchAll(mc));if(!n.length)return e;let o=new dt(e);for(let i of n){let s=i[2];if(!s)throw new Error("theme() expect exact one argument, but got 0");let a=hc(s,t,r);a&&o.overwrite(i.index,i.index+i[0].length,a)}return o.toString()}function hc(e,t,r=!0){let[n,o]=e.split("/"),s=n.trim().split(".").reduce((a,c)=>a?.[c],t);if(typeof s=="string"){if(o){let a=I(s);a&&(s=M(a,o))}return s}else if(r)throw new Error(`theme of "${e}" did not found`)}var Yr={};za(Yr,{auto:()=>xc,bracket:()=>Sc,bracketOfColor:()=>Cc,bracketOfLength:()=>Rc,bracketOfPosition:()=>Tc,cssvar:()=>Ec,degree:()=>zc,fraction:()=>kc,global:()=>Oc,number:()=>wc,numberWithUnit:()=>bc,percent:()=>$c,position:()=>Ac,properties:()=>Vc,px:()=>vc,rem:()=>yc,time:()=>jc});var ke=/^(-?\d*(?:\.\d+)?)(px|pt|pc|%|r?(?:em|ex|lh|cap|ch|ic)|(?:[sld]?v|cq)(?:[whib]|min|max)|in|cm|mm|rpx)?$/i,Hr=/^(-?\d*(?:\.\d+)?)$/,Gr=/^(px|[sld]?v[wh])$/i,qr={px:1,vw:100,vh:100,svw:100,svh:100,dvw:100,dvh:100,lvh:100,lvw:100},mt=/^\[(color|length|size|position|quoted|string):/i,io=/,(?![^()]*\))/g;var gc=["color","border-color","background-color","flex-grow","flex","flex-shrink","caret-color","font","gap","opacity","visibility","z-index","font-weight","zoom","text-shadow","transform","box-shadow","background-position","left","right","top","bottom","object-position","max-height","min-height","max-width","min-width","height","width","border-width","margin","padding","outline-width","outline-offset","font-size","line-height","text-indent","vertical-align","border-spacing","letter-spacing","word-spacing","stroke","filter","backdrop-filter","fill","mask","mask-size","mask-border","clip-path","clip","border-radius"];function G(e){return+e.toFixed(10)}function bc(e){let t=e.match(ke);if(!t)return;let[,r,n]=t,o=Number.parseFloat(r);if(n&&!Number.isNaN(o))return`${G(o)}${n}`}function xc(e){if(e==="auto"||e==="a")return"auto"}function yc(e){if(!e)return;if(Gr.test(e))return`${qr[e]}${e}`;let t=e.match(ke);if(!t)return;let[,r,n]=t,o=Number.parseFloat(r);if(!Number.isNaN(o))return o===0?"0":n?`${G(o)}${n}`:`${G(o/4)}rem`}function vc(e){if(Gr.test(e))return`${qr[e]}${e}`;let t=e.match(ke);if(!t)return;let[,r,n]=t,o=Number.parseFloat(r);if(!Number.isNaN(o))return n?`${G(o)}${n}`:`${G(o)}px`}function wc(e){if(!Hr.test(e))return;let t=Number.parseFloat(e);if(!Number.isNaN(t))return G(t)}function $c(e){if(e.endsWith("%")&&(e=e.slice(0,-1)),!Hr.test(e))return;let t=Number.parseFloat(e);if(!Number.isNaN(t))return`${G(t/100)}`}function kc(e){if(!e)return;if(e==="full")return"100%";let[t,r]=e.split("/"),n=Number.parseFloat(t)/Number.parseFloat(r);if(!Number.isNaN(n))return n===0?"0":`${G(n*100)}%`}function ht(e,t){if(e&&e.startsWith("[")&&e.endsWith("]")){let r,n,o=e.match(mt);if(o?(t||(n=o[1]),r=e.slice(o[0].length,-1)):r=e.slice(1,-1),!r||r==='=""')return;r.startsWith("--")&&(r=`var(${r})`);let i=0;for(let s of r)if(s==="[")i+=1;else if(s==="]"&&(i-=1,i<0))return;if(i)return;switch(n){case"string":return r.replace(/(^|[^\\])_/g,"$1 ").replace(/\\_/g,"_");case"quoted":return r.replace(/(^|[^\\])_/g,"$1 ").replace(/\\_/g,"_").replace(/(["\\])/g,"\\$1").replace(/^(.+)$/,'"$1"')}return r.replace(/(url\(.*?\))/g,s=>s.replace(/_/g,"\\_")).replace(/(^|[^\\])_/g,"$1 ").replace(/\\_/g,"_").replace(/(?:calc|clamp|max|min)\((.*)/g,s=>{let a=[];return s.replace(/var\((--.+?)[,)]/g,(c,u)=>(a.push(u),c.replace(u,"--un-calc"))).replace(/(-?\d*\.?\d(?!-\d.+[,)](?![^+\-/*])\D)(?:%|[a-z]+)?|\))([+\-/*])/g,"$1 $2 ").replace(/--un-calc/g,()=>a.shift())})}}function Sc(e){return ht(e)}function Cc(e){return ht(e,"color")}function Rc(e){return ht(e,"length")}function Tc(e){return ht(e,"position")}function Ec(e){if(/^\$[^\s'"`;{}]/.test(e)){let[t,r]=e.slice(1).split(",");return`var(--${Q(t)}${r?`, ${r}`:""})`}}function jc(e){let t=e.match(/^(-?[0-9.]+)(s|ms)?$/i);if(!t)return;let[,r,n]=t,o=Number.parseFloat(r);if(!Number.isNaN(o))return o===0&&!n?"0s":n?`${G(o)}${n}`:`${G(o)}ms`}function zc(e){let t=e.match(/^(-?[0-9.]+)(deg|rad|grad|turn)?$/i);if(!t)return;let[,r,n]=t,o=Number.parseFloat(r);if(!Number.isNaN(o))return o===0?"0":n?`${G(o)}${n}`:`${G(o)}deg`}function Oc(e){if(S.includes(e))return e}function Vc(e){if(e.split(",").every(t=>gc.includes(t)))return e}function Ac(e){if(["top","left","right","bottom","center"].includes(e))return e}var Mc=ut(Yr),l=Mc;var so={mid:"middle",base:"baseline",btm:"bottom",baseline:"baseline",top:"top",start:"top",middle:"middle",bottom:"bottom",end:"bottom","text-top":"text-top","text-bottom":"text-bottom",sub:"sub",super:"super",...Object.fromEntries(S.map(e=>[e,e]))},gt=[[/^(?:vertical|align|v)-([-\w]+%?)$/,([,e])=>({"vertical-align":so[e]??l.numberWithUnit(e)}),{autocomplete:[`(vertical|align|v)-(${Object.keys(so).join("|")})`,"(vertical|align|v)-<percentage>"]}]],ao=["center","left","right","justify","start","end"],bt=[...ao.map(e=>[`text-${e}`,{"text-align":e}]),...[...S,...ao].map(e=>[`text-align-${e}`,{"text-align":e}])];var uo="$$mini-no-negative";function _(e){return([t,r,n],{theme:o})=>{let i=o.spacing?.[n||"DEFAULT"]??l.bracket.cssvar.global.auto.fraction.rem(n);if(i!=null)return U[r].map(s=>[`${e}${s}`,i]);if(n?.startsWith("-")){let s=o.spacing?.[n.slice(1)];if(s!=null)return U[r].map(a=>[`${e}${a}`,`calc(${s} * -1)`])}}}function co(e,t,r="colors"){let n=e[r],o=-1;for(let i of t){if(o+=1,n&&typeof n!="string"){let s=t.slice(o).join("-").replace(/(-[a-z])/g,a=>a.slice(1).toUpperCase());if(n[s])return n[s];if(n[i]){n=n[i];continue}}return}return n}function xt(e,t,r){return co(e,t,r)||co(e,t,"colors")}function Zr(e,t){let[r,n]=ae(e,"[","]",["/",":"])??[];if(r!=null){let o=(r.match(mt)??[])[1];if(o==null||o===t)return[r,n]}}function Se(e,t,r){let n=Zr(e,"color");if(!n)return;let[o,i]=n,s=o.replace(/([a-z])(\d)/g,"$1-$2").split(/-/g),[a]=s;if(!a)return;let c,u=l.bracketOfColor(o),p=u||o;if(l.numberWithUnit(p))return;if(/^#[\da-f]+$/i.test(p)?c=p:/^hex-[\da-fA-F]+$/.test(p)?c=`#${p.slice(4)}`:o.startsWith("$")&&(c=l.cssvar(o)),c=c||u,!c){let d=xt(t,[o],r);typeof d=="string"&&(c=d)}let f="DEFAULT";if(!c){let d,[m]=s.slice(-1);/^\d+$/.test(m)?(f=m,d=xt(t,s.slice(0,-1),r),!d||typeof d=="string"?c=void 0:c=d[f]):(d=xt(t,s,r),!d&&s.length<=2&&([,f=f]=s,d=xt(t,[a],r)),typeof d=="string"?c=d:f&&d&&(c=d[f]))}return{opacity:i,name:a,no:f,color:c,cssColor:I(c),alpha:l.bracket.cssvar.percent(i??"")}}function j(e,t,r,n){return([,o],{theme:i})=>{let s=Se(o,i,r);if(!s)return;let{alpha:a,color:c,cssColor:u}=s,p={};if(u)if(a!=null)p[e]=M(u,a);else{let f=`--un-${t}-opacity`,d=M(u,`var(${f})`);d.includes(f)&&(p[f]=ee(u)),p[e]=d}else if(c)if(a!=null)p[e]=M(c,a);else{let f=`--un-${t}-opacity`,d=M(c,`var(${f})`);d.includes(f)&&(p[f]=1),p[e]=d}if(n?.(p)!==!1)return p}}function Ce(e,t){let r=[];e=O(e);for(let n=0;n<e.length;n++){let o=fe(e[n]," ",6);if(!o||o.length<3)return e;let i=!1,s=o.indexOf("inset");s!==-1&&(o.splice(s,1),i=!0);let a="",c=o.at(-1);if(I(o.at(0))){let u=I(o.shift());u&&(a=`, ${M(u)}`)}else if(I(c)){let u=I(o.pop());u&&(a=`, ${M(u)}`)}else c&&lt.test(c)&&(a=`, ${o.pop()}`);r.push(`${i?"inset ":""}${o.join(" ")} var(${t}${a})`)}return r}function Re(e,t,r){return e!=null&&!!Se(e,t,r)?.color}var lo=/[a-z]+/gi,Xr=new WeakMap;function pe({theme:e,generator:t},r="breakpoints"){let n=t?.userConfig?.theme?.[r]||e[r];if(!n)return;if(Xr.has(e))return Xr.get(e);let o=Object.entries(n).sort((i,s)=>Number.parseInt(i[1].replace(lo,""))-Number.parseInt(s[1].replace(lo,""))).map(([i,s])=>({point:i,size:s}));return Xr.set(e,o),o}function x(e,t){return S.map(r=>[`${e}-${r}`,{[t??e]:r}])}function q(e){return e!=null&&_e.test(e)}function fo(e){return e[0]==="["&&e.slice(-1)==="]"&&(e=e.slice(1,-1)),_e.test(e)||ke.test(e)}function yt(e,t,r){let n=t.split(io);return e||!e&&n.length===1?Yn[e].map(o=>[`--un-${r}${o}`,t]):n.map((o,i)=>[`--un-${r}-${Xn[i]}`,o])}var vt=[[/^outline-(?:width-|size-)?(.+)$/,po,{autocomplete:"outline-(width|size)-<num>"}],[/^outline-(?:color-)?(.+)$/,_c,{autocomplete:"outline-$colors"}],[/^outline-offset-(.+)$/,([,e],{theme:t})=>({"outline-offset":t.lineWidth?.[e]??l.bracket.cssvar.global.px(e)}),{autocomplete:"outline-(offset)-<num>"}],["outline",{"outline-style":"solid"}],...["auto","dashed","dotted","double","hidden","solid","groove","ridge","inset","outset",...S].map(e=>[`outline-${e}`,{"outline-style":e}]),["outline-none",{outline:"2px solid transparent","outline-offset":"2px"}]];function po([,e],{theme:t}){return{"outline-width":t.lineWidth?.[e]??l.bracket.cssvar.global.px(e)}}function _c(e,t){return q(l.bracket(e[1]))?po(e,t):j("outline-color","outline-color","borderColor")(e,t)}var wt=[["appearance-auto",{"-webkit-appearance":"auto",appearance:"auto"}],["appearance-none",{"-webkit-appearance":"none",appearance:"none"}]];function Pc(e){return l.properties.auto.global(e)??{contents:"contents",scroll:"scroll-position"}[e]}var $t=[[/^will-change-(.+)/,([,e])=>({"will-change":Pc(e)})]];var de=["solid","dashed","dotted","double","hidden","none","groove","ridge","inset","outset",...S],kt=[[/^(?:border|b)()(?:-(.+))?$/,X,{autocomplete:"(border|b)-<directions>"}],[/^(?:border|b)-([xy])(?:-(.+))?$/,X],[/^(?:border|b)-([rltbse])(?:-(.+))?$/,X],[/^(?:border|b)-(block|inline)(?:-(.+))?$/,X],[/^(?:border|b)-([bi][se])(?:-(.+))?$/,X],[/^(?:border|b)-()(?:width|size)-(.+)$/,X,{autocomplete:["(border|b)-<num>","(border|b)-<directions>-<num>"]}],[/^(?:border|b)-([xy])-(?:width|size)-(.+)$/,X],[/^(?:border|b)-([rltbse])-(?:width|size)-(.+)$/,X],[/^(?:border|b)-(block|inline)-(?:width|size)-(.+)$/,X],[/^(?:border|b)-([bi][se])-(?:width|size)-(.+)$/,X],[/^(?:border|b)-()(?:color-)?(.+)$/,Le,{autocomplete:["(border|b)-$colors","(border|b)-<directions>-$colors"]}],[/^(?:border|b)-([xy])-(?:color-)?(.+)$/,Le],[/^(?:border|b)-([rltbse])-(?:color-)?(.+)$/,Le],[/^(?:border|b)-(block|inline)-(?:color-)?(.+)$/,Le],[/^(?:border|b)-([bi][se])-(?:color-)?(.+)$/,Le],[/^(?:border|b)-()op(?:acity)?-?(.+)$/,Ue,{autocomplete:"(border|b)-(op|opacity)-<percent>"}],[/^(?:border|b)-([xy])-op(?:acity)?-?(.+)$/,Ue],[/^(?:border|b)-([rltbse])-op(?:acity)?-?(.+)$/,Ue],[/^(?:border|b)-(block|inline)-op(?:acity)?-?(.+)$/,Ue],[/^(?:border|b)-([bi][se])-op(?:acity)?-?(.+)$/,Ue],[/^(?:border-|b-)?(?:rounded|rd)()(?:-(.+))?$/,We,{autocomplete:["(border|b)-(rounded|rd)","(border|b)-(rounded|rd)-$borderRadius","(rounded|rd)","(rounded|rd)-$borderRadius"]}],[/^(?:border-|b-)?(?:rounded|rd)-([rltbse])(?:-(.+))?$/,We],[/^(?:border-|b-)?(?:rounded|rd)-([rltb]{2})(?:-(.+))?$/,We],[/^(?:border-|b-)?(?:rounded|rd)-([bise][se])(?:-(.+))?$/,We],[/^(?:border-|b-)?(?:rounded|rd)-([bi][se]-[bi][se])(?:-(.+))?$/,We],[/^(?:border|b)-(?:style-)?()(.+)$/,Ne,{autocomplete:["(border|b)-style",`(border|b)-(${de.join("|")})`,"(border|b)-<directions>-style",`(border|b)-<directions>-(${de.join("|")})`,`(border|b)-<directions>-style-(${de.join("|")})`,`(border|b)-style-(${de.join("|")})`]}],[/^(?:border|b)-([xy])-(?:style-)?(.+)$/,Ne],[/^(?:border|b)-([rltbse])-(?:style-)?(.+)$/,Ne],[/^(?:border|b)-(block|inline)-(?:style-)?(.+)$/,Ne],[/^(?:border|b)-([bi][se])-(?:style-)?(.+)$/,Ne]];function mo(e,t,r){if(t!=null)return{[`border${r}-color`]:M(e,t)};if(r===""){let n={},o="--un-border-opacity",i=M(e,`var(${o})`);return i.includes(o)&&(n[o]=typeof e=="string"?1:ee(e)),n["border-color"]=i,n}else{let n={},o="--un-border-opacity",i=`--un-border${r}-opacity`,s=M(e,`var(${i})`);return s.includes(i)&&(n[o]=typeof e=="string"?1:ee(e),n[i]=`var(${o})`),n[`border${r}-color`]=s,n}}function Fc(e){return([,t],r)=>{let n=Se(t,r,"borderColor");if(!n)return;let{alpha:o,color:i,cssColor:s}=n;if(s)return mo(s,o,e);if(i)return mo(i,o,e)}}function X([,e="",t],{theme:r}){let n=r.lineWidth?.[t||"DEFAULT"]??l.bracket.cssvar.global.px(t||"1");if(e in U&&n!=null)return U[e].map(o=>[`border${o}-width`,n])}function Le([,e="",t],r){if(e in U){if(q(l.bracket(t)))return X(["",e,t],r);if(Re(t,r.theme,"borderColor"))return Object.assign({},...U[e].map(n=>Fc(n)(["",t],r.theme)))}}function Ue([,e="",t]){let r=l.bracket.percent.cssvar(t);if(e in U&&r!=null)return U[e].map(n=>[`--un-border${n}-opacity`,r])}function We([,e="",t],{theme:r}){let n=r.borderRadius?.[t||"DEFAULT"]||l.bracket.cssvar.global.fraction.rem(t||"1");if(e in Nr&&n!=null)return Nr[e].map(o=>[`border${o}-radius`,n])}function Ne([,e="",t]){if(de.includes(t)&&e in U)return U[e].map(r=>[`border${r}-style`,t])}var St=[[/^op(?:acity)?-?(.+)$/,([,e])=>({opacity:l.bracket.percent.cssvar(e)})]],Lc=/^\[url\(.+\)\]$/,Uc=/^\[(?:length|size):.+\]$/,Wc=/^\[position:.+\]$/,Nc=/^\[(?:linear|conic|radial)-gradient\(.+\)\]$/,Ct=[[/^bg-(.+)$/,(...e)=>{let t=e[0][1];return Lc.test(t)?{"--un-url":l.bracket(t),"background-image":"var(--un-url)"}:Uc.test(t)&&l.bracketOfLength(t)!=null?{"background-size":l.bracketOfLength(t).split(" ").map(r=>l.fraction.auto.px.cssvar(r)??r).join(" ")}:(fo(t)||Wc.test(t))&&l.bracketOfPosition(t)!=null?{"background-position":l.bracketOfPosition(t).split(" ").map(r=>l.position.fraction.auto.px.cssvar(r)??r).join(" ")}:Nc.test(t)?{"background-image":l.bracket(t)}:j("background-color","bg","backgroundColor")(...e)},{autocomplete:"bg-$colors"}],[/^bg-op(?:acity)?-?(.+)$/,([,e])=>({"--un-bg-opacity":l.bracket.percent.cssvar(e)}),{autocomplete:"bg-(op|opacity)-<percent>"}]],ho=[[/^color-scheme-(\w+)$/,([,e])=>({"color-scheme":e})]];var Rt=[[/^@container(?:\/(\w+))?(?:-(normal))?$/,([,e,t])=>(se("The container query rule is experimental and may not follow semver."),{"container-type":t??"inline-size","container-name":e})]];var go=["solid","double","dotted","dashed","wavy",...S],Tt=[[/^(?:decoration-)?(underline|overline|line-through)$/,([,e])=>({"text-decoration-line":e}),{autocomplete:"decoration-(underline|overline|line-through)"}],[/^(?:underline|decoration)-(?:size-)?(.+)$/,bo,{autocomplete:"(underline|decoration)-<num>"}],[/^(?:underline|decoration)-(auto|from-font)$/,([,e])=>({"text-decoration-thickness":e}),{autocomplete:"(underline|decoration)-(auto|from-font)"}],[/^(?:underline|decoration)-(.+)$/,Bc,{autocomplete:"(underline|decoration)-$colors"}],[/^(?:underline|decoration)-op(?:acity)?-?(.+)$/,([,e])=>({"--un-line-opacity":l.bracket.percent.cssvar(e)}),{autocomplete:"(underline|decoration)-(op|opacity)-<percent>"}],[/^(?:underline|decoration)-offset-(.+)$/,([,e],{theme:t})=>({"text-underline-offset":t.lineWidth?.[e]??l.auto.bracket.cssvar.global.px(e)}),{autocomplete:"(underline|decoration)-(offset)-<num>"}],...go.map(e=>[`underline-${e}`,{"text-decoration-style":e}]),...go.map(e=>[`decoration-${e}`,{"text-decoration-style":e}]),["no-underline",{"text-decoration":"none"}],["decoration-none",{"text-decoration":"none"}]];function bo([,e],{theme:t}){return{"text-decoration-thickness":t.lineWidth?.[e]??l.bracket.cssvar.global.px(e)}}function Bc(e,t){if(q(l.bracket(e[1])))return bo(e,t);let r=j("text-decoration-color","line","borderColor")(e,t);if(r)return{"-webkit-text-decoration-color":r["text-decoration-color"],...r}}var Et={all:"all",colors:["color","background-color","border-color","outline-color","text-decoration-color","fill","stroke"].join(","),none:"none",opacity:"opacity",shadow:"box-shadow",transform:"transform"};function xo(e){let t=e.split(",").flatMap(r=>l.properties(r)??Et[r]);if(t.length>0&&t.every(Boolean))return t.join(",")}var jt=[[/^transition(?:-([a-z-]+(?:,[a-z-]+)*))?(?:-(\d+))?$/,([,e,t],{theme:r})=>{let n=e!=null?xo(e):[Et.colors,"opacity","box-shadow","transform","filter","backdrop-filter"].join(",");if(n){let o=r.duration?.[t||"DEFAULT"]??l.time(t||"150");return{"transition-property":n,"transition-timing-function":"cubic-bezier(0.4, 0, 0.2, 1)","transition-duration":o}}},{autocomplete:`transition-(${Object.keys(Et).join("|")})`}],[/^(?:transition-)?duration-(.+)$/,([,e],{theme:t})=>({"transition-duration":t.duration?.[e||"DEFAULT"]??l.bracket.cssvar.time(e)}),{autocomplete:["transition-duration-$duration","duration-$duration"]}],[/^(?:transition-)?delay-(.+)$/,([,e],{theme:t})=>({"transition-delay":t.duration?.[e||"DEFAULT"]??l.bracket.cssvar.time(e)}),{autocomplete:["transition-delay-$duration","delay-$duration"]}],[/^(?:transition-)?ease(?:-(.+))?$/,([,e],{theme:t})=>({"transition-timing-function":t.easing?.[e||"DEFAULT"]??l.bracket.cssvar(e)}),{autocomplete:["transition-ease-(linear|in|out|in-out|DEFAULT)","ease-(linear|in|out|in-out|DEFAULT)"]}],[/^(?:transition-)?property-(.+)$/,([,e])=>({"transition-property":l.bracket.global(e)||xo(e)}),{autocomplete:[`transition-property-(${[...S,...Object.keys(Et)].join("|")})`]}],["transition-none",{transition:"none"}],...x("transition")];var zt=[["flex",{display:"flex"}],["inline-flex",{display:"inline-flex"}],["flex-inline",{display:"inline-flex"}],[/^flex-(.*)$/,([,e])=>({flex:l.bracket(e)!=null?l.bracket(e).split(" ").map(t=>l.cssvar.fraction(t)??t).join(" "):l.cssvar.fraction(e)})],["flex-1",{flex:"1 1 0%"}],["flex-auto",{flex:"1 1 auto"}],["flex-initial",{flex:"0 1 auto"}],["flex-none",{flex:"none"}],[/^(?:flex-)?shrink(?:-(.*))?$/,([,e=""])=>({"flex-shrink":l.bracket.cssvar.number(e)??1}),{autocomplete:["flex-shrink-<num>","shrink-<num>"]}],[/^(?:flex-)?grow(?:-(.*))?$/,([,e=""])=>({"flex-grow":l.bracket.cssvar.number(e)??1}),{autocomplete:["flex-grow-<num>","grow-<num>"]}],[/^(?:flex-)?basis-(.+)$/,([,e],{theme:t})=>({"flex-basis":t.spacing?.[e]??l.bracket.cssvar.auto.fraction.rem(e)}),{autocomplete:["flex-basis-$spacing","basis-$spacing"]}],["flex-row",{"flex-direction":"row"}],["flex-row-reverse",{"flex-direction":"row-reverse"}],["flex-col",{"flex-direction":"column"}],["flex-col-reverse",{"flex-direction":"column-reverse"}],["flex-wrap",{"flex-wrap":"wrap"}],["flex-wrap-reverse",{"flex-wrap":"wrap-reverse"}],["flex-nowrap",{"flex-wrap":"nowrap"}]];var Ot=[[/^text-(.+)$/,Ic,{autocomplete:"text-$fontSize"}],[/^(?:text|font)-size-(.+)$/,yo,{autocomplete:"text-size-$fontSize"}],[/^text-(?:color-)?(.+)$/,Dc,{autocomplete:"text-$colors"}],[/^(?:color|c)-(.+)$/,j("color","text","textColor"),{autocomplete:"(color|c)-$colors"}],[/^(?:text|color|c)-(.+)$/,([,e])=>S.includes(e)?{color:e}:void 0,{autocomplete:`(text|color|c)-(${S.join("|")})`}],[/^(?:text|color|c)-op(?:acity)?-?(.+)$/,([,e])=>({"--un-text-opacity":l.bracket.percent.cssvar(e)}),{autocomplete:"(text|color|c)-(op|opacity)-<percent>"}],[/^(?:font|fw)-?([^-]+)$/,([,e],{theme:t})=>({"font-weight":t.fontWeight?.[e]||l.bracket.global.number(e)}),{autocomplete:["(font|fw)-(100|200|300|400|500|600|700|800|900)","(font|fw)-$fontWeight"]}],[/^(?:font-)?(?:leading|lh|line-height)-(.+)$/,([,e],{theme:t})=>({"line-height":Jr(e,t,"lineHeight")}),{autocomplete:"(leading|lh|line-height)-$lineHeight"}],["font-synthesis-weight",{"font-synthesis":"weight"}],["font-synthesis-style",{"font-synthesis":"style"}],["font-synthesis-small-caps",{"font-synthesis":"small-caps"}],["font-synthesis-none",{"font-synthesis":"none"}],[/^font-synthesis-(.+)$/,([,e])=>({"font-synthesis":l.bracket.cssvar.global(e)})],[/^(?:font-)?tracking-(.+)$/,([,e],{theme:t})=>({"letter-spacing":t.letterSpacing?.[e]||l.bracket.cssvar.global.rem(e)}),{autocomplete:"tracking-$letterSpacing"}],[/^(?:font-)?word-spacing-(.+)$/,([,e],{theme:t})=>({"word-spacing":t.wordSpacing?.[e]||l.bracket.cssvar.global.rem(e)}),{autocomplete:"word-spacing-$wordSpacing"}],["font-stretch-normal",{"font-stretch":"normal"}],["font-stretch-ultra-condensed",{"font-stretch":"ultra-condensed"}],["font-stretch-extra-condensed",{"font-stretch":"extra-condensed"}],["font-stretch-condensed",{"font-stretch":"condensed"}],["font-stretch-semi-condensed",{"font-stretch":"semi-condensed"}],["font-stretch-semi-expanded",{"font-stretch":"semi-expanded"}],["font-stretch-expanded",{"font-stretch":"expanded"}],["font-stretch-extra-expanded",{"font-stretch":"extra-expanded"}],["font-stretch-ultra-expanded",{"font-stretch":"ultra-expanded"}],[/^font-stretch-(.+)$/,([,e])=>({"font-stretch":l.bracket.cssvar.fraction.global(e)}),{autocomplete:"font-stretch-<percentage>"}],[/^font-(.+)$/,([,e],{theme:t})=>({"font-family":t.fontFamily?.[e]||l.bracket.cssvar.global(e)}),{autocomplete:"font-$fontFamily"}]],Vt=[[/^tab(?:-(.+))?$/,([,e])=>{let t=l.bracket.cssvar.global.number(e||"4");if(t!=null)return{"-moz-tab-size":t,"-o-tab-size":t,"tab-size":t}}]],At=[[/^indent(?:-(.+))?$/,([,e],{theme:t})=>({"text-indent":t.textIndent?.[e||"DEFAULT"]||l.bracket.cssvar.global.fraction.rem(e)}),{autocomplete:"indent-$textIndent"}]],Mt=[[/^text-stroke(?:-(.+))?$/,([,e],{theme:t})=>({"-webkit-text-stroke-width":t.textStrokeWidth?.[e||"DEFAULT"]||l.bracket.cssvar.px(e)}),{autocomplete:"text-stroke-$textStrokeWidth"}],[/^text-stroke-(.+)$/,j("-webkit-text-stroke-color","text-stroke","borderColor"),{autocomplete:"text-stroke-$colors"}],[/^text-stroke-op(?:acity)?-?(.+)$/,([,e])=>({"--un-text-stroke-opacity":l.bracket.percent.cssvar(e)}),{autocomplete:"text-stroke-(op|opacity)-<percent>"}]],_t=[[/^text-shadow(?:-(.+))?$/,([,e],{theme:t})=>{let r=t.textShadow?.[e||"DEFAULT"];return r!=null?{"--un-text-shadow":Ce(r,"--un-text-shadow-color").join(","),"text-shadow":"var(--un-text-shadow)"}:{"text-shadow":l.bracket.cssvar.global(e)}},{autocomplete:"text-shadow-$textShadow"}],[/^text-shadow-color-(.+)$/,j("--un-text-shadow-color","text-shadow","shadowColor"),{autocomplete:"text-shadow-color-$colors"}],[/^text-shadow-color-op(?:acity)?-?(.+)$/,([,e])=>({"--un-text-shadow-opacity":l.bracket.percent.cssvar(e)}),{autocomplete:"text-shadow-color-(op|opacity)-<percent>"}]];function Jr(e,t,r){return t[r]?.[e]||l.bracket.cssvar.global.rem(e)}function yo([,e],{theme:t}){let n=O(t.fontSize?.[e])?.[0]??l.bracket.cssvar.global.rem(e);if(n!=null)return{"font-size":n}}function Dc(e,t){return q(l.bracket(e[1]))?yo(e,t):j("color","text","textColor")(e,t)}function Ic([,e="base"],{theme:t}){let r=Zr(e,"length");if(!r)return;let[n,o]=r,i=O(t.fontSize?.[n]),s=o?Jr(o,t,"lineHeight"):void 0;if(i?.[0]){let[c,u,p]=i;return typeof u=="object"?{"font-size":c,...u}:{"font-size":c,"line-height":s??u??"1","letter-spacing":p?Jr(p,t,"letterSpacing"):void 0}}let a=l.bracketOfLength.rem(n);return s&&a?{"font-size":a,"line-height":s}:{"font-size":l.bracketOfLength.rem(e)}}var Kc={"":"",x:"column-",y:"row-",col:"column-",row:"row-"};function Qr([,e="",t],{theme:r}){let n=r.spacing?.[t]??l.bracket.cssvar.global.rem(t);if(n!=null)return{[`${Kc[e]}gap`]:n}}var Pt=[[/^(?:flex-|grid-)?gap-?()(.+)$/,Qr,{autocomplete:["gap-$spacing","gap-<num>"]}],[/^(?:flex-|grid-)?gap-([xy])-?(.+)$/,Qr,{autocomplete:["gap-(x|y)-$spacing","gap-(x|y)-<num>"]}],[/^(?:flex-|grid-)?gap-(col|row)-?(.+)$/,Qr,{autocomplete:["gap-(col|row)-$spacing","gap-(col|row)-<num>"]}]];function J(e){return e.replace("col","column")}function en(e){return e[0]==="r"?"Row":"Column"}function Hc(e,t,r){let n=t[`gridAuto${en(e)}`]?.[r];if(n!=null)return n;switch(r){case"min":return"min-content";case"max":return"max-content";case"fr":return"minmax(0,1fr)"}return l.bracket.cssvar.auto.rem(r)}var Ft=[["grid",{display:"grid"}],["inline-grid",{display:"inline-grid"}],[/^(?:grid-)?(row|col)-(.+)$/,([,e,t],{theme:r})=>({[`grid-${J(e)}`]:r[`grid${en(e)}`]?.[t]??l.bracket.cssvar.auto(t)})],[/^(?:grid-)?(row|col)-span-(.+)$/,([,e,t])=>{if(t==="full")return{[`grid-${J(e)}`]:"1/-1"};let r=l.bracket.number(t);if(r!=null)return{[`grid-${J(e)}`]:`span ${r}/span ${r}`}},{autocomplete:"(grid-row|grid-col|row|col)-span-<num>"}],[/^(?:grid-)?(row|col)-start-(.+)$/,([,e,t])=>({[`grid-${J(e)}-start`]:l.bracket.cssvar(t)??t})],[/^(?:grid-)?(row|col)-end-(.+)$/,([,e,t])=>({[`grid-${J(e)}-end`]:l.bracket.cssvar(t)??t}),{autocomplete:"(grid-row|grid-col|row|col)-(start|end)-<num>"}],[/^(?:grid-)?auto-(rows|cols)-(.+)$/,([,e,t],{theme:r})=>({[`grid-auto-${J(e)}`]:Hc(e,r,t)}),{autocomplete:"(grid-auto|auto)-(rows|cols)-<num>"}],[/^(?:grid-auto-flow|auto-flow|grid-flow)-(.+)$/,([,e])=>({"grid-auto-flow":l.bracket.cssvar(e)})],[/^(?:grid-auto-flow|auto-flow|grid-flow)-(row|col|dense|row-dense|col-dense)$/,([,e])=>({"grid-auto-flow":J(e).replace("-"," ")}),{autocomplete:["(grid-auto-flow|auto-flow|grid-flow)-(row|col|dense|row-dense|col-dense)"]}],[/^(?:grid-)?(rows|cols)-(.+)$/,([,e,t],{theme:r})=>({[`grid-template-${J(e)}`]:r[`gridTemplate${en(e)}`]?.[t]??l.bracket.cssvar(t)})],[/^(?:grid-)?(rows|cols)-minmax-([\w.-]+)$/,([,e,t])=>({[`grid-template-${J(e)}`]:`repeat(auto-fill,minmax(${t},1fr))`})],[/^(?:grid-)?(rows|cols)-(\d+)$/,([,e,t])=>({[`grid-template-${J(e)}`]:`repeat(${t},minmax(0,1fr))`}),{autocomplete:"(grid-rows|grid-cols|rows|cols)-<num>"}],[/^grid-area(s)?-(.+)$/,([,e,t])=>e!=null?{"grid-template-areas":l.cssvar(t)??t.split("-").map(r=>`"${l.bracket(r)}"`).join(" ")}:{"grid-area":l.bracket.cssvar(t)}],["grid-rows-none",{"grid-template-rows":"none"}],["grid-cols-none",{"grid-template-columns":"none"}],["grid-rows-subgrid",{"grid-template-rows":"subgrid"}],["grid-cols-subgrid",{"grid-template-columns":"subgrid"}]];var Lt=["auto","hidden","clip","visible","scroll","overlay",...S],Ut=[[/^(?:overflow|of)-(.+)$/,([,e])=>Lt.includes(e)?{overflow:e}:void 0,{autocomplete:[`(overflow|of)-(${Lt.join("|")})`,`(overflow|of)-(x|y)-(${Lt.join("|")})`]}],[/^(?:overflow|of)-([xy])-(.+)$/,([,e,t])=>Lt.includes(t)?{[`overflow-${e}`]:t}:void 0]];var Wt=[[/^(?:position-|pos-)?(relative|absolute|fixed|sticky)$/,([,e])=>({position:e}),{autocomplete:["(position|pos)-<position>","(position|pos)-<globalKeyword>","<position>"]}],[/^(?:position-|pos-)([-\w]+)$/,([,e])=>S.includes(e)?{position:e}:void 0],[/^(?:position-|pos-)?(static)$/,([,e])=>({position:e})]],De=[["justify-start",{"justify-content":"flex-start"}],["justify-end",{"justify-content":"flex-end"}],["justify-center",{"justify-content":"center"}],["justify-between",{"justify-content":"space-between"}],["justify-around",{"justify-content":"space-around"}],["justify-evenly",{"justify-content":"space-evenly"}],["justify-stretch",{"justify-content":"stretch"}],["justify-left",{"justify-content":"left"}],["justify-right",{"justify-content":"right"}],...x("justify","justify-content"),["justify-items-start",{"justify-items":"start"}],["justify-items-end",{"justify-items":"end"}],["justify-items-center",{"justify-items":"center"}],["justify-items-stretch",{"justify-items":"stretch"}],...x("justify-items"),["justify-self-auto",{"justify-self":"auto"}],["justify-self-start",{"justify-self":"start"}],["justify-self-end",{"justify-self":"end"}],["justify-self-center",{"justify-self":"center"}],["justify-self-stretch",{"justify-self":"stretch"}],...x("justify-self")],Nt=[[/^order-(.+)$/,([,e])=>({order:l.bracket.cssvar.number(e)})],["order-first",{order:"-9999"}],["order-last",{order:"9999"}],["order-none",{order:"0"}]],Ie=[["content-center",{"align-content":"center"}],["content-start",{"align-content":"flex-start"}],["content-end",{"align-content":"flex-end"}],["content-between",{"align-content":"space-between"}],["content-around",{"align-content":"space-around"}],["content-evenly",{"align-content":"space-evenly"}],...x("content","align-content"),["items-start",{"align-items":"flex-start"}],["items-end",{"align-items":"flex-end"}],["items-center",{"align-items":"center"}],["items-baseline",{"align-items":"baseline"}],["items-stretch",{"align-items":"stretch"}],...x("items","align-items"),["self-auto",{"align-self":"auto"}],["self-start",{"align-self":"flex-start"}],["self-end",{"align-self":"flex-end"}],["self-center",{"align-self":"center"}],["self-stretch",{"align-self":"stretch"}],["self-baseline",{"align-self":"baseline"}],...x("self","align-self")],Ke=[["place-content-center",{"place-content":"center"}],["place-content-start",{"place-content":"start"}],["place-content-end",{"place-content":"end"}],["place-content-between",{"place-content":"space-between"}],["place-content-around",{"place-content":"space-around"}],["place-content-evenly",{"place-content":"space-evenly"}],["place-content-stretch",{"place-content":"stretch"}],...x("place-content"),["place-items-start",{"place-items":"start"}],["place-items-end",{"place-items":"end"}],["place-items-center",{"place-items":"center"}],["place-items-stretch",{"place-items":"stretch"}],...x("place-items"),["place-self-auto",{"place-self":"auto"}],["place-self-start",{"place-self":"start"}],["place-self-end",{"place-self":"end"}],["place-self-center",{"place-self":"center"}],["place-self-stretch",{"place-self":"stretch"}],...x("place-self")],Bt=[...De,...Ie,...Ke].flatMap(([e,t])=>[[`flex-${e}`,t],[`grid-${e}`,t]]);function tn(e,{theme:t}){return t.spacing?.[e]??l.bracket.cssvar.global.auto.fraction.rem(e)}function Be([,e,t],r){let n=tn(t,r);if(n!=null&&e in Wr)return Wr[e].map(o=>[o.slice(1),n])}var Dt=[[/^(?:position-|pos-)?inset-(.+)$/,([,e],t)=>({inset:tn(e,t)}),{autocomplete:["(position|pos)-inset-<directions>-$spacing","(position|pos)-inset-(block|inline)-$spacing","(position|pos)-inset-(bs|be|is|ie)-$spacing","(position|pos)-(top|left|right|bottom)-$spacing"]}],[/^(?:position-|pos-)?(start|end)-(.+)$/,Be],[/^(?:position-|pos-)?inset-([xy])-(.+)$/,Be],[/^(?:position-|pos-)?inset-([rltbse])-(.+)$/,Be],[/^(?:position-|pos-)?inset-(block|inline)-(.+)$/,Be],[/^(?:position-|pos-)?inset-([bi][se])-(.+)$/,Be],[/^(?:position-|pos-)?(top|left|right|bottom)-(.+)$/,([,e,t],r)=>({[e]:tn(t,r)})]],It=[["float-left",{float:"left"}],["float-right",{float:"right"}],["float-none",{float:"none"}],...x("float"),["clear-left",{clear:"left"}],["clear-right",{clear:"right"}],["clear-both",{clear:"both"}],["clear-none",{clear:"none"}],...x("clear")],Kt=[[/^(?:position-|pos-)?z([\d.]+)$/,([,e])=>({"z-index":l.number(e)})],[/^(?:position-|pos-)?z-(.+)$/,([,e],{theme:t})=>({"z-index":t.zIndex?.[e]??l.bracket.cssvar.global.auto.number(e)}),{autocomplete:"z-<num>"}]],Ht=[["box-border",{"box-sizing":"border-box"}],["box-content",{"box-sizing":"content-box"}],...x("box","box-sizing")];var Gc=["auto","default","none","context-menu","help","pointer","progress","wait","cell","crosshair","text","vertical-text","alias","copy","move","no-drop","not-allowed","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out"],qc=["none","strict","content","size","inline-size","layout","style","paint"],E=" ",Gt=[["inline",{display:"inline"}],["block",{display:"block"}],["inline-block",{display:"inline-block"}],["contents",{display:"contents"}],["flow-root",{display:"flow-root"}],["list-item",{display:"list-item"}],["hidden",{display:"none"}],[/^display-(.+)$/,([,e])=>({display:l.bracket.cssvar.global(e)})]],qt=[["visible",{visibility:"visible"}],["invisible",{visibility:"hidden"}],["backface-visible",{"backface-visibility":"visible"}],["backface-hidden",{"backface-visibility":"hidden"}],...x("backface","backface-visibility")],Yt=[[/^cursor-(.+)$/,([,e])=>({cursor:l.bracket.cssvar.global(e)})],...Gc.map(e=>[`cursor-${e}`,{cursor:e}])],Xt=[[/^contain-(.*)$/,([,e])=>l.bracket(e)!=null?{contain:l.bracket(e).split(" ").map(t=>l.cssvar.fraction(t)??t).join(" ")}:qc.includes(e)?{contain:e}:void 0]],Zt=[["pointer-events-auto",{"pointer-events":"auto"}],["pointer-events-none",{"pointer-events":"none"}],...x("pointer-events")],Jt=[["resize-x",{resize:"horizontal"}],["resize-y",{resize:"vertical"}],["resize",{resize:"both"}],["resize-none",{resize:"none"}],...x("resize")],Qt=[["select-auto",{"-webkit-user-select":"auto","user-select":"auto"}],["select-all",{"-webkit-user-select":"all","user-select":"all"}],["select-text",{"-webkit-user-select":"text","user-select":"text"}],["select-none",{"-webkit-user-select":"none","user-select":"none"}],...x("select","user-select")],er=[[/^(?:whitespace-|ws-)([-\w]+)$/,([,e])=>["normal","nowrap","pre","pre-line","pre-wrap","break-spaces",...S].includes(e)?{"white-space":e}:void 0,{autocomplete:"(whitespace|ws)-(normal|nowrap|pre|pre-line|pre-wrap|break-spaces)"}]],tr=[[/^intrinsic-size-(.+)$/,([,e])=>({"contain-intrinsic-size":l.bracket.cssvar.global.fraction.rem(e)}),{autocomplete:"intrinsic-size-<num>"}],["content-visibility-visible",{"content-visibility":"visible"}],["content-visibility-hidden",{"content-visibility":"hidden"}],["content-visibility-auto",{"content-visibility":"auto"}],...x("content-visibility")],rr=[[/^content-(.+)$/,([,e])=>({content:l.bracket.cssvar(e)})],["content-empty",{content:'""'}],["content-none",{content:"none"}]],nr=[["break-normal",{"overflow-wrap":"normal","word-break":"normal"}],["break-words",{"overflow-wrap":"break-word"}],["break-all",{"word-break":"break-all"}],["break-keep",{"word-break":"keep-all"}],["break-anywhere",{"overflow-wrap":"anywhere"}]],or=[["text-wrap",{"text-wrap":"wrap"}],["text-nowrap",{"text-wrap":"nowrap"}],["text-balance",{"text-wrap":"balance"}],["text-pretty",{"text-wrap":"pretty"}]],ir=[["truncate",{overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}],["text-truncate",{overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}],["text-ellipsis",{"text-overflow":"ellipsis"}],["text-clip",{"text-overflow":"clip"}]],sr=[["case-upper",{"text-transform":"uppercase"}],["case-lower",{"text-transform":"lowercase"}],["case-capital",{"text-transform":"capitalize"}],["case-normal",{"text-transform":"none"}],...x("case","text-transform")],ar=[["italic",{"font-style":"italic"}],["not-italic",{"font-style":"normal"}],["font-italic",{"font-style":"italic"}],["font-not-italic",{"font-style":"normal"}],["oblique",{"font-style":"oblique"}],["not-oblique",{"font-style":"normal"}],["font-oblique",{"font-style":"oblique"}],["font-not-oblique",{"font-style":"normal"}]],cr=[["antialiased",{"-webkit-font-smoothing":"antialiased","-moz-osx-font-smoothing":"grayscale"}],["subpixel-antialiased",{"-webkit-font-smoothing":"auto","-moz-osx-font-smoothing":"auto"}]];var lr={"--un-ring-inset":E,"--un-ring-offset-width":"0px","--un-ring-offset-color":"#fff","--un-ring-width":"0px","--un-ring-color":"rgb(147 197 253 / 0.5)","--un-shadow":"0 0 rgb(0 0 0 / 0)"},ur=[[/^ring(?:-(.+))?$/,([,e],{theme:t})=>{let r=t.ringWidth?.[e||"DEFAULT"]??l.px(e||"1");if(r)return{"--un-ring-width":r,"--un-ring-offset-shadow":"var(--un-ring-inset) 0 0 0 var(--un-ring-offset-width) var(--un-ring-offset-color)","--un-ring-shadow":"var(--un-ring-inset) 0 0 0 calc(var(--un-ring-width) + var(--un-ring-offset-width)) var(--un-ring-color)","box-shadow":"var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow)"}},{autocomplete:"ring-$ringWidth"}],[/^ring-(?:width-|size-)(.+)$/,vo,{autocomplete:"ring-(width|size)-$lineWidth"}],["ring-offset",{"--un-ring-offset-width":"1px"}],[/^ring-offset-(?:width-|size-)?(.+)$/,([,e],{theme:t})=>({"--un-ring-offset-width":t.lineWidth?.[e]??l.bracket.cssvar.px(e)}),{autocomplete:"ring-offset-(width|size)-$lineWidth"}],[/^ring-(.+)$/,Yc,{autocomplete:"ring-$colors"}],[/^ring-op(?:acity)?-?(.+)$/,([,e])=>({"--un-ring-opacity":l.bracket.percent.cssvar(e)}),{autocomplete:"ring-(op|opacity)-<percent>"}],[/^ring-offset-(.+)$/,j("--un-ring-offset-color","ring-offset","borderColor"),{autocomplete:"ring-offset-$colors"}],[/^ring-offset-op(?:acity)?-?(.+)$/,([,e])=>({"--un-ring-offset-opacity":l.bracket.percent.cssvar(e)}),{autocomplete:"ring-offset-(op|opacity)-<percent>"}],["ring-inset",{"--un-ring-inset":"inset"}]];function vo([,e],{theme:t}){return{"--un-ring-width":t.ringWidth?.[e]??l.bracket.cssvar.px(e)}}function Yc(e,t){return q(l.bracket(e[1]))?vo(e,t):j("--un-ring-color","ring","borderColor")(e,t)}var fr={"--un-ring-offset-shadow":"0 0 rgb(0 0 0 / 0)","--un-ring-shadow":"0 0 rgb(0 0 0 / 0)","--un-shadow-inset":E,"--un-shadow":"0 0 rgb(0 0 0 / 0)"},pr=[[/^shadow(?:-(.+))?$/,(e,t)=>{let[,r]=e,{theme:n}=t,o=n.boxShadow?.[r||"DEFAULT"],i=r?l.bracket.cssvar(r):void 0;return(o!=null||i!=null)&&!Re(i,n,"shadowColor")?{"--un-shadow":Ce(o||i,"--un-shadow-color").join(","),"box-shadow":"var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow)"}:j("--un-shadow-color","shadow","shadowColor")(e,t)},{autocomplete:["shadow-$colors","shadow-$boxShadow"]}],[/^shadow-op(?:acity)?-?(.+)$/,([,e])=>({"--un-shadow-opacity":l.bracket.percent.cssvar(e)}),{autocomplete:"shadow-(op|opacity)-<percent>"}],["shadow-inset",{"--un-shadow-inset":"inset"}]];var Xc={h:"height",w:"width",inline:"inline-size",block:"block-size"};function me(e,t){return`${e||""}${Xc[t]}`}function dr(e,t,r,n){let o=me(e,t).replace(/-(\w)/g,(s,a)=>a.toUpperCase()),i=r[o]?.[n];if(i!=null)return i;switch(n){case"fit":case"max":case"min":return`${n}-content`}return l.bracket.cssvar.global.auto.fraction.rem(n)}var mr=[[/^size-(min-|max-)?(.+)$/,([,e,t],{theme:r})=>({[me(e,"w")]:dr(e,"w",r,t),[me(e,"h")]:dr(e,"h",r,t)})],[/^(?:size-)?(min-|max-)?([wh])-?(.+)$/,([,e,t,r],{theme:n})=>({[me(e,t)]:dr(e,t,n,r)})],[/^(?:size-)?(min-|max-)?(block|inline)-(.+)$/,([,e,t,r],{theme:n})=>({[me(e,t)]:dr(e,t,n,r)}),{autocomplete:["(w|h)-$width|height|maxWidth|maxHeight|minWidth|minHeight|inlineSize|blockSize|maxInlineSize|maxBlockSize|minInlineSize|minBlockSize","(block|inline)-$width|height|maxWidth|maxHeight|minWidth|minHeight|inlineSize|blockSize|maxInlineSize|maxBlockSize|minInlineSize|minBlockSize","(max|min)-(w|h|block|inline)","(max|min)-(w|h|block|inline)-$width|height|maxWidth|maxHeight|minWidth|minHeight|inlineSize|blockSize|maxInlineSize|maxBlockSize|minInlineSize|minBlockSize","(w|h)-full","(max|min)-(w|h)-full"]}],[/^(?:size-)?(min-|max-)?(h)-screen-(.+)$/,([,e,t,r],n)=>({[me(e,t)]:wo(n,r,"verticalBreakpoints")})],[/^(?:size-)?(min-|max-)?(w)-screen-(.+)$/,([,e,t,r],n)=>({[me(e,t)]:wo(n,r)}),{autocomplete:["(w|h)-screen","(min|max)-(w|h)-screen","h-screen-$verticalBreakpoints","(min|max)-h-screen-$verticalBreakpoints","w-screen-$breakpoints","(min|max)-w-screen-$breakpoints"]}]];function wo(e,t,r="breakpoints"){let n=pe(e,r);if(n)return n.find(o=>o.point===t)?.size}function Zc(e){if(/^\d+\/\d+$/.test(e))return e;switch(e){case"square":return"1/1";case"video":return"16/9"}return l.bracket.cssvar.global.auto.number(e)}var hr=[[/^(?:size-)?aspect-(?:ratio-)?(.+)$/,([,e])=>({"aspect-ratio":Zc(e)}),{autocomplete:["aspect-(square|video|ratio)","aspect-ratio-(square|video)"]}]];var gr=[[/^pa?()-?(.+)$/,_("padding"),{autocomplete:["(m|p)<num>","(m|p)-<num>"]}],[/^p-?xy()()$/,_("padding"),{autocomplete:"(m|p)-(xy)"}],[/^p-?([xy])(?:-?(.+))?$/,_("padding")],[/^p-?([rltbse])(?:-?(.+))?$/,_("padding"),{autocomplete:"(m|p)<directions>-<num>"}],[/^p-(block|inline)(?:-(.+))?$/,_("padding"),{autocomplete:"(m|p)-(block|inline)-<num>"}],[/^p-?([bi][se])(?:-?(.+))?$/,_("padding"),{autocomplete:"(m|p)-(bs|be|is|ie)-<num>"}]],br=[[/^ma?()-?(.+)$/,_("margin")],[/^m-?xy()()$/,_("margin")],[/^m-?([xy])(?:-?(.+))?$/,_("margin")],[/^m-?([rltbse])(?:-?(.+))?$/,_("margin")],[/^m-(block|inline)(?:-(.+))?$/,_("margin")],[/^m-?([bi][se])(?:-?(.+))?$/,_("margin")]];var xr=["translate","rotate","scale"],Jc=["translateX(var(--un-translate-x))","translateY(var(--un-translate-y))","rotate(var(--un-rotate))","rotateZ(var(--un-rotate-z))","skewX(var(--un-skew-x))","skewY(var(--un-skew-y))","scaleX(var(--un-scale-x))","scaleY(var(--un-scale-y))"].join(" "),Te=["translateX(var(--un-translate-x))","translateY(var(--un-translate-y))","translateZ(var(--un-translate-z))","rotate(var(--un-rotate))","rotateX(var(--un-rotate-x))","rotateY(var(--un-rotate-y))","rotateZ(var(--un-rotate-z))","skewX(var(--un-skew-x))","skewY(var(--un-skew-y))","scaleX(var(--un-scale-x))","scaleY(var(--un-scale-y))","scaleZ(var(--un-scale-z))"].join(" "),Qc=["translate3d(var(--un-translate-x), var(--un-translate-y), var(--un-translate-z))","rotate(var(--un-rotate))","rotateX(var(--un-rotate-x))","rotateY(var(--un-rotate-y))","rotateZ(var(--un-rotate-z))","skewX(var(--un-skew-x))","skewY(var(--un-skew-y))","scaleX(var(--un-scale-x))","scaleY(var(--un-scale-y))","scaleZ(var(--un-scale-z))"].join(" "),yr={"--un-rotate":0,"--un-rotate-x":0,"--un-rotate-y":0,"--un-rotate-z":0,"--un-scale-x":1,"--un-scale-y":1,"--un-scale-z":1,"--un-skew-x":0,"--un-skew-y":0,"--un-translate-x":0,"--un-translate-y":0,"--un-translate-z":0},vr=[[/^(?:transform-)?origin-(.+)$/,([,e])=>({"transform-origin":L[e]??l.bracket.cssvar(e)}),{autocomplete:[`transform-origin-(${Object.keys(L).join("|")})`,`origin-(${Object.keys(L).join("|")})`]}],[/^(?:transform-)?perspect(?:ive)?-(.+)$/,([,e])=>{let t=l.bracket.cssvar.px.numberWithUnit(e);if(t!=null)return{"-webkit-perspective":t,perspective:t}}],[/^(?:transform-)?perspect(?:ive)?-origin-(.+)$/,([,e])=>{let t=l.bracket.cssvar(e)??(e.length>=3?L[e]:void 0);if(t!=null)return{"-webkit-perspective-origin":t,"perspective-origin":t}}],[/^(?:transform-)?translate-()(.+)$/,$o],[/^(?:transform-)?translate-([xyz])-(.+)$/,$o],[/^(?:transform-)?rotate-()(.+)$/,So],[/^(?:transform-)?rotate-([xyz])-(.+)$/,So],[/^(?:transform-)?skew-()(.+)$/,Co],[/^(?:transform-)?skew-([xy])-(.+)$/,Co,{autocomplete:["transform-skew-(x|y)-<percent>","skew-(x|y)-<percent>"]}],[/^(?:transform-)?scale-()(.+)$/,ko],[/^(?:transform-)?scale-([xyz])-(.+)$/,ko,{autocomplete:[`transform-(${xr.join("|")})-<percent>`,`transform-(${xr.join("|")})-(x|y|z)-<percent>`,`(${xr.join("|")})-<percent>`,`(${xr.join("|")})-(x|y|z)-<percent>`]}],[/^(?:transform-)?preserve-3d$/,()=>({"transform-style":"preserve-3d"})],[/^(?:transform-)?preserve-flat$/,()=>({"transform-style":"flat"})],["transform",{transform:Te}],["transform-cpu",{transform:Jc}],["transform-gpu",{transform:Qc}],["transform-none",{transform:"none"}],...x("transform")];function $o([,e,t],{theme:r}){let n=r.spacing?.[t]??l.bracket.cssvar.fraction.rem(t);if(n!=null)return[...yt(e,n,"translate"),["transform",Te]]}function ko([,e,t]){let r=l.bracket.cssvar.fraction.percent(t);if(r!=null)return[...yt(e,r,"scale"),["transform",Te]]}function So([,e="",t]){let r=l.bracket.cssvar.degree(t);if(r!=null)return e?{"--un-rotate":0,[`--un-rotate-${e}`]:r,transform:Te}:{"--un-rotate-x":0,"--un-rotate-y":0,"--un-rotate-z":0,"--un-rotate":r,transform:Te}}function Co([,e,t]){let r=l.bracket.cssvar.degree(t);if(r!=null)return[...yt(e,r,"skew"),["transform",Te]]}var el={backface:"backface-visibility",break:"word-break",case:"text-transform",content:"align-content",fw:"font-weight",items:"align-items",justify:"justify-content",select:"user-select",self:"align-self",vertical:"vertical-align",visible:"visibility",whitespace:"white-space",ws:"white-space"},wr=[[/^(.+?)-(\$.+)$/,([,e,t])=>{let r=el[e];if(r)return{[r]:l.cssvar(t)}}]],$r=[[/^\[(.*)\]$/,([e,t])=>{if(!t.includes(":"))return;let[r,...n]=t.split(":"),o=n.join(":");if(!rl(t)&&/^[a-z-]+$/.test(r)&&tl(o)){let i=l.bracket(`[${o}]`);if(i)return{[r]:i}}}]];function tl(e){let t=0;function r(n){for(;t<e.length;)if(t+=1,e[t]===n)return!0;return!1}for(t=0;t<e.length;t++){let n=e[t];if("\"`'".includes(n)){if(!r(n))return!1}else if(n==="("){if(!r(")"))return!1}else if("[]{}:".includes(n))return!1}return!0}function rl(e){if(!e.includes("://"))return!1;try{return new URL(e).host!==""}catch{return!1}}var kr=[[/^(where|\?)$/,(e,{constructCSS:t,generator:r})=>{if(r.userConfig.envMode==="dev")return`@keyframes __un_qm{0%{box-shadow:inset 4px 4px #ff1e90, inset -4px -4px #ff1e90}100%{box-shadow:inset 8px 8px #3399ff, inset -8px -8px #3399ff}} ${t({animation:"__un_qm 0.5s ease-in-out alternate infinite"})}`}]];var Sr=[[/^fill-(.+)$/,j("fill","fill","backgroundColor"),{autocomplete:"fill-$colors"}],[/^fill-op(?:acity)?-?(.+)$/,([,e])=>({"--un-fill-opacity":l.bracket.percent.cssvar(e)}),{autocomplete:"fill-(op|opacity)-<percent>"}],["fill-none",{fill:"none"}],[/^stroke-(?:width-|size-)?(.+)$/,Ro,{autocomplete:["stroke-width-$lineWidth","stroke-size-$lineWidth"]}],[/^stroke-dash-(.+)$/,([,e])=>({"stroke-dasharray":l.bracket.cssvar.number(e)}),{autocomplete:"stroke-dash-<num>"}],[/^stroke-offset-(.+)$/,([,e],{theme:t})=>({"stroke-dashoffset":t.lineWidth?.[e]??l.bracket.cssvar.px.numberWithUnit(e)}),{autocomplete:"stroke-offset-$lineWidth"}],[/^stroke-(.+)$/,nl,{autocomplete:"stroke-$colors"}],[/^stroke-op(?:acity)?-?(.+)$/,([,e])=>({"--un-stroke-opacity":l.bracket.percent.cssvar(e)}),{autocomplete:"stroke-(op|opacity)-<percent>"}],["stroke-cap-square",{"stroke-linecap":"square"}],["stroke-cap-round",{"stroke-linecap":"round"}],["stroke-cap-auto",{"stroke-linecap":"butt"}],["stroke-join-arcs",{"stroke-linejoin":"arcs"}],["stroke-join-bevel",{"stroke-linejoin":"bevel"}],["stroke-join-clip",{"stroke-linejoin":"miter-clip"}],["stroke-join-round",{"stroke-linejoin":"round"}],["stroke-join-auto",{"stroke-linejoin":"miter"}],["stroke-none",{stroke:"none"}]];function Ro([,e],{theme:t}){return{"stroke-width":t.lineWidth?.[e]??l.bracket.cssvar.fraction.px.number(e)}}function nl(e,t){return q(l.bracket(e[1]))?Ro(e,t):j("stroke","stroke","borderColor")(e,t)}var To=[wr,$r,gr,br,Gt,St,Ct,ho,Sr,kt,tr,rr,Ot,Vt,At,ir,Tt,Mt,_t,sr,bt,ar,cr,pr,ur,zt,Ft,Pt,Wt,mr,hr,Yt,qt,Zt,Jt,gt,Qt,er,nr,Ut,vt,wt,Nt,De,Ie,Ke,Bt,Dt,It,Kt,Ht,jt,vr,$t,Rt,Xt,or,kr].flat(1);var rn={inherit:"inherit",current:"currentColor",transparent:"transparent",black:"#000",white:"#fff",rose:{50:"#fff1f2",100:"#ffe4e6",200:"#fecdd3",300:"#fda4af",400:"#fb7185",500:"#f43f5e",600:"#e11d48",700:"#be123c",800:"#9f1239",900:"#881337",950:"#4c0519"},pink:{50:"#fdf2f8",100:"#fce7f3",200:"#fbcfe8",300:"#f9a8d4",400:"#f472b6",500:"#ec4899",600:"#db2777",700:"#be185d",800:"#9d174d",900:"#831843",950:"#500724"},fuchsia:{50:"#fdf4ff",100:"#fae8ff",200:"#f5d0fe",300:"#f0abfc",400:"#e879f9",500:"#d946ef",600:"#c026d3",700:"#a21caf",800:"#86198f",900:"#701a75",950:"#4a044e"},purple:{50:"#faf5ff",100:"#f3e8ff",200:"#e9d5ff",300:"#d8b4fe",400:"#c084fc",500:"#a855f7",600:"#9333ea",700:"#7e22ce",800:"#6b21a8",900:"#581c87",950:"#3b0764"},violet:{50:"#f5f3ff",100:"#ede9fe",200:"#ddd6fe",300:"#c4b5fd",400:"#a78bfa",500:"#8b5cf6",600:"#7c3aed",700:"#6d28d9",800:"#5b21b6",900:"#4c1d95",950:"#2e1065"},indigo:{50:"#eef2ff",100:"#e0e7ff",200:"#c7d2fe",300:"#a5b4fc",400:"#818cf8",500:"#6366f1",600:"#4f46e5",700:"#4338ca",800:"#3730a3",900:"#312e81",950:"#1e1b4b"},blue:{50:"#eff6ff",100:"#dbeafe",200:"#bfdbfe",300:"#93c5fd",400:"#60a5fa",500:"#3b82f6",600:"#2563eb",700:"#1d4ed8",800:"#1e40af",900:"#1e3a8a",950:"#172554"},sky:{50:"#f0f9ff",100:"#e0f2fe",200:"#bae6fd",300:"#7dd3fc",400:"#38bdf8",500:"#0ea5e9",600:"#0284c7",700:"#0369a1",800:"#075985",900:"#0c4a6e",950:"#082f49"},cyan:{50:"#ecfeff",100:"#cffafe",200:"#a5f3fc",300:"#67e8f9",400:"#22d3ee",500:"#06b6d4",600:"#0891b2",700:"#0e7490",800:"#155e75",900:"#164e63",950:"#083344"},teal:{50:"#f0fdfa",100:"#ccfbf1",200:"#99f6e4",300:"#5eead4",400:"#2dd4bf",500:"#14b8a6",600:"#0d9488",700:"#0f766e",800:"#115e59",900:"#134e4a",950:"#042f2e"},emerald:{50:"#ecfdf5",100:"#d1fae5",200:"#a7f3d0",300:"#6ee7b7",400:"#34d399",500:"#10b981",600:"#059669",700:"#047857",800:"#065f46",900:"#064e3b",950:"#022c22"},green:{50:"#f0fdf4",100:"#dcfce7",200:"#bbf7d0",300:"#86efac",400:"#4ade80",500:"#22c55e",600:"#16a34a",700:"#15803d",800:"#166534",900:"#14532d",950:"#052e16"},lime:{50:"#f7fee7",100:"#ecfccb",200:"#d9f99d",300:"#bef264",400:"#a3e635",500:"#84cc16",600:"#65a30d",700:"#4d7c0f",800:"#3f6212",900:"#365314",950:"#1a2e05"},yellow:{50:"#fefce8",100:"#fef9c3",200:"#fef08a",300:"#fde047",400:"#facc15",500:"#eab308",600:"#ca8a04",700:"#a16207",800:"#854d0e",900:"#713f12",950:"#422006"},amber:{50:"#fffbeb",100:"#fef3c7",200:"#fde68a",300:"#fcd34d",400:"#fbbf24",500:"#f59e0b",600:"#d97706",700:"#b45309",800:"#92400e",900:"#78350f",950:"#451a03"},orange:{50:"#fff7ed",100:"#ffedd5",200:"#fed7aa",300:"#fdba74",400:"#fb923c",500:"#f97316",600:"#ea580c",700:"#c2410c",800:"#9a3412",900:"#7c2d12",950:"#431407"},red:{50:"#fef2f2",100:"#fee2e2",200:"#fecaca",300:"#fca5a5",400:"#f87171",500:"#ef4444",600:"#dc2626",700:"#b91c1c",800:"#991b1b",900:"#7f1d1d",950:"#450a0a"},gray:{50:"#f9fafb",100:"#f3f4f6",200:"#e5e7eb",300:"#d1d5db",400:"#9ca3af",500:"#6b7280",600:"#4b5563",700:"#374151",800:"#1f2937",900:"#111827",950:"#030712"},slate:{50:"#f8fafc",100:"#f1f5f9",200:"#e2e8f0",300:"#cbd5e1",400:"#94a3b8",500:"#64748b",600:"#475569",700:"#334155",800:"#1e293b",900:"#0f172a",950:"#020617"},zinc:{50:"#fafafa",100:"#f4f4f5",200:"#e4e4e7",300:"#d4d4d8",400:"#a1a1aa",500:"#71717a",600:"#52525b",700:"#3f3f46",800:"#27272a",900:"#18181b",950:"#09090b"},neutral:{50:"#fafafa",100:"#f5f5f5",200:"#e5e5e5",300:"#d4d4d4",400:"#a3a3a3",500:"#737373",600:"#525252",700:"#404040",800:"#262626",900:"#171717",950:"#0a0a0a"},stone:{50:"#fafaf9",100:"#f5f5f4",200:"#e7e5e4",300:"#d6d3d1",400:"#a8a29e",500:"#78716c",600:"#57534e",700:"#44403c",800:"#292524",900:"#1c1917",950:"#0c0a09"},light:{50:"#fdfdfd",100:"#fcfcfc",200:"#fafafa",300:"#f8f9fa",400:"#f6f6f6",500:"#f2f2f2",600:"#f1f3f5",700:"#e9ecef",800:"#dee2e6",900:"#dde1e3",950:"#d8dcdf"},dark:{50:"#4a4a4a",100:"#3c3c3c",200:"#323232",300:"#2d2d2d",400:"#222222",500:"#1f1f1f",600:"#1c1c1e",700:"#1b1b1b",800:"#181818",900:"#0f0f0f",950:"#080808"},get lightblue(){return this.sky},get lightBlue(){return this.sky},get warmgray(){return this.stone},get warmGray(){return this.stone},get truegray(){return this.neutral},get trueGray(){return this.neutral},get coolgray(){return this.gray},get coolGray(){return this.gray},get bluegray(){return this.slate},get blueGray(){return this.slate}};Object.values(rn).forEach(e=>{typeof e!="string"&&e!==void 0&&(e.DEFAULT=e.DEFAULT||e[400],Object.keys(e).forEach(t=>{let r=+t/100;r===Math.round(r)&&(e[r]=e[t])}))});var Eo={sans:["ui-sans-serif","system-ui","-apple-system","BlinkMacSystemFont",'"Segoe UI"',"Roboto",'"Helvetica Neue"',"Arial",'"Noto Sans"',"sans-serif",'"Apple Color Emoji"','"Segoe UI Emoji"','"Segoe UI Symbol"','"Noto Color Emoji"'].join(","),serif:["ui-serif","Georgia","Cambria",'"Times New Roman"',"Times","serif"].join(","),mono:["ui-monospace","SFMono-Regular","Menlo","Monaco","Consolas",'"Liberation Mono"','"Courier New"',"monospace"].join(",")},jo={xs:["0.75rem","1rem"],sm:["0.875rem","1.25rem"],base:["1rem","1.5rem"],lg:["1.125rem","1.75rem"],xl:["1.25rem","1.75rem"],"2xl":["1.5rem","2rem"],"3xl":["1.875rem","2.25rem"],"4xl":["2.25rem","2.5rem"],"5xl":["3rem","1"],"6xl":["3.75rem","1"],"7xl":["4.5rem","1"],"8xl":["6rem","1"],"9xl":["8rem","1"]},zo={DEFAULT:"1.5rem",xs:"0.5rem",sm:"1rem",md:"1.5rem",lg:"2rem",xl:"2.5rem","2xl":"3rem","3xl":"4rem"},Oo={DEFAULT:"1.5rem",none:"0",sm:"thin",md:"medium",lg:"thick"},Vo={DEFAULT:["0 0 1px rgb(0 0 0 / 0.2)","0 0 1px rgb(1 0 5 / 0.1)"],none:"0 0 rgb(0 0 0 / 0)",sm:"1px 1px 3px rgb(36 37 47 / 0.25)",md:["0 1px 2px rgb(30 29 39 / 0.19)","1px 2px 4px rgb(54 64 147 / 0.18)"],lg:["3px 3px 6px rgb(0 0 0 / 0.26)","0 0 5px rgb(15 3 86 / 0.22)"],xl:["1px 1px 3px rgb(0 0 0 / 0.29)","2px 4px 7px rgb(73 64 125 / 0.35)"]},Ao={none:"1",tight:"1.25",snug:"1.375",normal:"1.5",relaxed:"1.625",loose:"2"},nn={tighter:"-0.05em",tight:"-0.025em",normal:"0em",wide:"0.025em",wider:"0.05em",widest:"0.1em"},Mo={thin:"100",extralight:"200",light:"300",normal:"400",medium:"500",semibold:"600",bold:"700",extrabold:"800",black:"900"},_o=nn;var on={sm:"640px",md:"768px",lg:"1024px",xl:"1280px","2xl":"1536px"},Po={...on},Fo={DEFAULT:"1px",none:"0"},Lo={DEFAULT:"1rem",none:"0",xs:"0.75rem",sm:"0.875rem",lg:"1.125rem",xl:"1.25rem","2xl":"1.5rem","3xl":"1.875rem","4xl":"2.25rem","5xl":"3rem","6xl":"3.75rem","7xl":"4.5rem","8xl":"6rem","9xl":"8rem"},Uo={DEFAULT:"150ms",none:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms"},Wo={DEFAULT:"0.25rem",none:"0",sm:"0.125rem",md:"0.375rem",lg:"0.5rem",xl:"0.75rem","2xl":"1rem","3xl":"1.5rem",full:"9999px"},No={DEFAULT:["var(--un-shadow-inset) 0 1px 3px 0 rgb(0 0 0 / 0.1)","var(--un-shadow-inset) 0 1px 2px -1px rgb(0 0 0 / 0.1)"],none:"0 0 rgb(0 0 0 / 0)",sm:"var(--un-shadow-inset) 0 1px 2px 0 rgb(0 0 0 / 0.05)",md:["var(--un-shadow-inset) 0 4px 6px -1px rgb(0 0 0 / 0.1)","var(--un-shadow-inset) 0 2px 4px -2px rgb(0 0 0 / 0.1)"],lg:["var(--un-shadow-inset) 0 10px 15px -3px rgb(0 0 0 / 0.1)","var(--un-shadow-inset) 0 4px 6px -4px rgb(0 0 0 / 0.1)"],xl:["var(--un-shadow-inset) 0 20px 25px -5px rgb(0 0 0 / 0.1)","var(--un-shadow-inset) 0 8px 10px -6px rgb(0 0 0 / 0.1)"],"2xl":"var(--un-shadow-inset) 0 25px 50px -12px rgb(0 0 0 / 0.25)",inner:"inset 0 2px 4px 0 rgb(0 0 0 / 0.05)"},Bo={DEFAULT:"cubic-bezier(0.4, 0, 0.2, 1)",linear:"linear",in:"cubic-bezier(0.4, 0, 1, 1)",out:"cubic-bezier(0, 0, 0.2, 1)","in-out":"cubic-bezier(0.4, 0, 0.2, 1)"},Do={DEFAULT:"3px",none:"0"},Io={auto:"auto"},Ko={mouse:"(hover) and (pointer: fine)"};var Ho={DEFAULT:"8px",0:"0",sm:"4px",md:"12px",lg:"16px",xl:"24px","2xl":"40px","3xl":"64px"},Go={DEFAULT:["0 1px 2px rgb(0 0 0 / 0.1)","0 1px 1px rgb(0 0 0 / 0.06)"],sm:"0 1px 1px rgb(0 0 0 / 0.05)",md:["0 4px 3px rgb(0 0 0 / 0.07)","0 2px 2px rgb(0 0 0 / 0.06)"],lg:["0 10px 8px rgb(0 0 0 / 0.04)","0 4px 3px rgb(0 0 0 / 0.1)"],xl:["0 20px 13px rgb(0 0 0 / 0.03)","0 8px 5px rgb(0 0 0 / 0.08)"],"2xl":"0 25px 25px rgb(0 0 0 / 0.15)",none:"0 0 rgb(0 0 0 / 0)"};var He={xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",prose:"65ch"},sn={auto:"auto",...He,screen:"100vw"},Ge={none:"none",...He,screen:"100vw"},an={auto:"auto",...He,screen:"100vh"},qe={none:"none",...He,screen:"100vh"},qo=Object.fromEntries(Object.entries(He).map(([e,t])=>[e,`(min-width: ${t})`]));var Yo={...yr,...fr,...lr};var Xo={width:sn,height:an,maxWidth:Ge,maxHeight:qe,minWidth:Ge,minHeight:qe,inlineSize:sn,blockSize:an,maxInlineSize:Ge,maxBlockSize:qe,minInlineSize:Ge,minBlockSize:qe,colors:rn,fontFamily:Eo,fontSize:jo,fontWeight:Mo,breakpoints:on,verticalBreakpoints:Po,borderRadius:Wo,lineHeight:Ao,letterSpacing:nn,wordSpacing:_o,boxShadow:No,textIndent:zo,textShadow:Vo,textStrokeWidth:Oo,blur:Ho,dropShadow:Go,easing:Bo,lineWidth:Fo,spacing:Lo,duration:Uo,ringWidth:Do,preflightBase:Yo,containers:qo,zIndex:Io,media:Ko};var Zo={name:"aria",match(e,t){let r=W("aria-",e,t.generator.config.separators);if(r){let[n,o]=r,i=l.bracket(n)??t.theme.aria?.[n]??"";if(i)return{matcher:o,selector:s=>`${s}[aria-${i}]`}}}};function Cr(e){return{name:`${e}-aria`,match(t,r){let n=W(`${e}-aria-`,t,r.generator.config.separators);if(n){let[o,i]=n,s=l.bracket(o)??r.theme.aria?.[o]??"";if(s)return{matcher:`${e}-[[aria-${s}]]:${i}`}}}}}var Jo=[Cr("group"),Cr("peer"),Cr("parent"),Cr("previous")];function Qo(e){let t=e.match(/^-?\d+\.?\d*/)?.[0]||"",r=e.slice(t.length);if(r==="px"){let n=Number.parseFloat(t)-.1;return Number.isNaN(n)?e:`${n}${r}`}return`calc(${e} - 0.1px)`}var ei=/(max|min)-\[([^\]]*)\]:/;function ti(){let e={};return{name:"breakpoints",match(t,r){if(ei.test(t)){let o=t.match(ei);return{matcher:t.replace(o[0],""),handle:(s,a)=>a({...s,parent:`${s.parent?`${s.parent} $$ `:""}@media (${o[1]}-width: ${o[2]})`})}}let n=(pe(r)??[]).map(({point:o,size:i},s)=>[o,i,s]);for(let[o,i,s]of n){e[o]||(e[o]=new RegExp(`^((?:([al]t-|[<~]|max-))?${o}(?:${r.generator.config.separators.join("|")}))`));let a=t.match(e[o]);if(!a)continue;let[,c]=a,u=t.slice(c.length);if(u==="container")continue;let p=c.startsWith("lt-")||c.startsWith("<")||c.startsWith("max-"),f=c.startsWith("at-")||c.startsWith("~"),d=3e3;return p?(d-=s+1,{matcher:u,handle:(m,$)=>$({...m,parent:`${m.parent?`${m.parent} $$ `:""}@media (max-width: ${Qo(i)})`,parentOrder:d})}):(d+=s+1,f&&s<n.length-1?{matcher:u,handle:(m,$)=>$({...m,parent:`${m.parent?`${m.parent} $$ `:""}@media (min-width: ${i}) and (max-width: ${Qo(n[s+1][1])})`,parentOrder:d})}:{matcher:u,handle:(m,$)=>$({...m,parent:`${m.parent?`${m.parent} $$ `:""}@media (min-width: ${i})`,parentOrder:d})})}},multiPass:!0,autocomplete:"(at-|lt-|max-|)$breakpoints:"}}var ri=[N("*",e=>({selector:`${e.selector} > *`}))];function Ye(e,t){return{name:`combinator:${e}`,match(r,n){if(!r.startsWith(e))return;let o=n.generator.config.separators,i=ce(`${e}-`,r,o);if(!i){for(let a of o)if(r.startsWith(`${e}${a}`)){i=["",r.slice(e.length+a.length)];break}if(!i)return}let s=l.bracket(i[0])??"";return s===""&&(s="*"),{matcher:i[1],selector:a=>`${a}${t}${s}`}},multiPass:!0}}var ni=[Ye("all"," "),Ye("children",">"),Ye("next","+"),Ye("sibling","+"),Ye("siblings","~")];var oi={name:"@",match(e,t){if(e.startsWith("@container"))return;let r=W("@",e,t.generator.config.separators);if(r){let[n,o,i]=r,s=l.bracket(n),a;if(s){let c=l.numberWithUnit(s);c&&(a=`(min-width: ${c})`)}else a=t.theme.containers?.[n]??"";if(a){se("The container query variant is experimental and may not follow semver.");let c=1e3+Object.keys(t.theme.containers??{}).indexOf(n);return i&&(c+=1e3),{matcher:o,handle:(u,p)=>p({...u,parent:`${u.parent?`${u.parent} $$ `:""}@container${i?` ${i} `:" "}${a}`,parentOrder:c})}}}},multiPass:!0};function ii(e={}){if(e?.dark==="class"||typeof e.dark=="object"){let{dark:t=".dark",light:r=".light"}=typeof e.dark=="string"?{}:e.dark;return[N("dark",n=>({prefix:`${t} $$ ${n.prefix}`})),N("light",n=>({prefix:`${r} $$ ${n.prefix}`}))]}return[B("dark","@media (prefers-color-scheme: dark)"),B("light","@media (prefers-color-scheme: light)")]}var si={name:"data",match(e,t){let r=W("data-",e,t.generator.config.separators);if(r){let[n,o]=r,i=l.bracket(n)??t.theme.data?.[n]??"";if(i)return{matcher:o,selector:s=>`${s}[data-${i}]`}}}};function Rr(e){return{name:`${e}-data`,match(t,r){let n=W(`${e}-data-`,t,r.generator.config.separators);if(n){let[o,i]=n,s=l.bracket(o)??r.theme.data?.[o]??"";if(s)return{matcher:`${e}-[[data-${s}]]:${i}`}}}}}var ai=[Rr("group"),Rr("peer"),Rr("parent"),Rr("previous")];var ci=[N("rtl",e=>({prefix:`[dir="rtl"] $$ ${e.prefix}`})),N("ltr",e=>({prefix:`[dir="ltr"] $$ ${e.prefix}`}))];var li={name:"selector",match(e,t){let r=ce("selector-",e,t.generator.config.separators);if(r){let[n,o]=r,i=l.bracket(n);if(i)return{matcher:o,selector:()=>i}}}},ui={name:"layer",match(e,t){let r=W("layer-",e,t.generator.config.separators);if(r){let[n,o]=r,i=l.bracket(n)??n;if(i)return{matcher:o,handle:(s,a)=>a({...s,parent:`${s.parent?`${s.parent} $$ `:""}@layer ${i}`})}}}},fi={name:"uno-layer",match(e,t){let r=W("uno-layer-",e,t.generator.config.separators);if(r){let[n,o]=r,i=l.bracket(n)??n;if(i)return{matcher:o,layer:i}}}},pi={name:"scope",match(e,t){let r=ce("scope-",e,t.generator.config.separators);if(r){let[n,o]=r,i=l.bracket(n);if(i)return{matcher:o,selector:s=>`${i} $$ ${s}`}}}},di={name:"variables",match(e,t){if(!e.startsWith("["))return;let[r,n]=we(e,"[","]")??[];if(!(r&&n))return;let o;for(let a of t.generator.config.separators)if(n.startsWith(a)){o=n.slice(a.length);break}if(o==null)return;let i=l.bracket(r)??"",s=i.startsWith("@");if(s||i.includes("&"))return{matcher:o,handle(a,c){let u=s?{parent:`${a.parent?`${a.parent} $$ `:""}${i}`}:{selector:i.replace(/&/g,a.selector)};return c({...a,...u})}}},multiPass:!0},mi={name:"theme-variables",match(e,t){if(no(e))return{matcher:e,handle(r,n){return n({...r,entries:JSON.parse(oo(JSON.stringify(r.entries),t.theme))})}}}};var hi=/^-?[0-9.]+(?:[a-z]+|%)?$/,gi=/-?[0-9.]+(?:[a-z]+|%)?/,ol=[/\b(opacity|color|flex|backdrop-filter|^filter|transform)\b/];function il(e){let t=e.match(_e)||e.match(lt);if(t){let[r,n]=ae(`(${t[2]})${t[3]}`,"(",")"," ")??[];if(r)return`calc(${t[1]}${r} * -1)${n?` ${n}`:""}`}}var sl=/\b(hue-rotate)\s*(\(.*)/;function al(e){let t=e.match(sl);if(t){let[r,n]=ae(t[2],"(",")"," ")??[];if(r){let o=hi.test(r.slice(1,-1))?r.replace(gi,i=>i.startsWith("-")?i.slice(1):`-${i}`):`(calc(${r} * -1))`;return`${t[1]}${o}${n?` ${n}`:""}`}}}var bi={name:"negative",match(e){if(e.startsWith("-"))return{matcher:e.slice(1),body:t=>{if(t.find(n=>n[0]===uo))return;let r=!1;return t.forEach(n=>{let o=n[1]?.toString();if(!o||o==="0"||ol.some(a=>a.test(n[0])))return;let i=il(o);if(i){n[1]=i,r=!0;return}let s=al(o);if(s){n[1]=s,r=!0;return}hi.test(o)&&(n[1]=o.replace(gi,a=>a.startsWith("-")?a.slice(1):`-${a}`),r=!0)}),r?t:[]}}}};function xi(){let e;return{name:"important",match(t,r){e||(e=new RegExp(`^(important(?:${r.generator.config.separators.join("|")})|!)`));let n,o=t.match(e);if(o?n=t.slice(o[0].length):t.endsWith("!")&&(n=t.slice(0,-1)),n)return{matcher:n,body:i=>(i.forEach(s=>{s[1]&&(s[1]+=" !important")}),i)}}}}var yi=B("print","@media print"),vi={name:"media",match(e,t){let r=W("media-",e,t.generator.config.separators);if(r){let[n,o]=r,i=l.bracket(n)??"";if(i===""&&(i=t.theme.media?.[n]??""),i)return{matcher:o,handle:(s,a)=>a({...s,parent:`${s.parent?`${s.parent} $$ `:""}@media ${i}`})}}},multiPass:!0};var wi={name:"supports",match(e,t){let r=W("supports-",e,t.generator.config.separators);if(r){let[n,o]=r,i=l.bracket(n)??"";if(i===""&&(i=t.theme.supports?.[n]??""),i)return{matcher:o,handle:(s,a)=>a({...s,parent:`${s.parent?`${s.parent} $$ `:""}@supports ${i}`})}}},multiPass:!0};var Ee=Object.fromEntries([["first-letter","::first-letter"],["first-line","::first-line"],"any-link","link","visited","target",["open","[open]"],"default","checked","indeterminate","placeholder-shown","autofill","optional","required","valid","invalid","user-valid","user-invalid","in-range","out-of-range","read-only","read-write","empty","focus-within","hover","focus","focus-visible","active","enabled","disabled","popover-open","root","empty",["even-of-type",":nth-of-type(even)"],["even",":nth-child(even)"],["odd-of-type",":nth-of-type(odd)"],["odd",":nth-child(odd)"],"first-of-type",["first",":first-child"],"last-of-type",["last",":last-child"],"only-child","only-of-type",["backdrop-element","::backdrop"],["placeholder","::placeholder"],["before","::before"],["after","::after"],["selection"," ::selection"],["marker"," ::marker"],["file","::file-selector-button"]].map(e=>Array.isArray(e)?e:[e,`:${e}`])),Si=Object.keys(Ee),je=Object.fromEntries([["backdrop","::backdrop"]].map(e=>Array.isArray(e)?e:[e,`:${e}`])),Ci=Object.keys(je),cl=["not","is","where","has"],cn=Object.entries(Ee).filter(([,e])=>!e.startsWith("::")).map(([e])=>e).sort((e,t)=>t.length-e.length).join("|"),ln=Object.entries(je).filter(([,e])=>!e.startsWith("::")).map(([e])=>e).sort((e,t)=>t.length-e.length).join("|"),he=cl.join("|");function ll(e,t,r){let n=new RegExp(`^(${ie(t)}:)(\\S+)${ie(r)}\\1`),o,i,s,a,c=f=>{let d=ce(`${e}-`,f,[]);if(!d)return;let[m,$]=d,C=l.bracket(m);if(C==null)return;let k=$.split(o,1)?.[0]??"",z=`${t}${Q(k)}`;return[k,f.slice(f.length-($.length-k.length-1)),C.includes("&")?C.replace(/&/g,z):`${z}${C}`]},u=f=>{let d=f.match(i)||f.match(s);if(!d)return;let[m,$,C]=d,k=d[3]??"",z=Ee[C]||je[C]||`:${C}`;return $&&(z=`:${$}(${z})`),[k,f.slice(m.length),`${t}${Q(k)}${z}`,C]},p=f=>{let d=f.match(a);if(!d)return;let[m,$,C]=d,k=d[3]??"",z=`:${$}(${C})`;return[k,f.slice(m.length),`${t}${Q(k)}${z}`]};return{name:`pseudo:${e}`,match(f,d){if(o&&i&&s||(o=new RegExp(`(?:${d.generator.config.separators.join("|")})`),i=new RegExp(`^${e}-(?:(?:(${he})-)?(${cn}))(?:(/\\w+))?(?:${d.generator.config.separators.join("|")})`),s=new RegExp(`^${e}-(?:(?:(${he})-)?(${ln}))(?:(/\\w+))?(?:${d.generator.config.separators.filter(g=>g!=="-").join("|")})`),a=new RegExp(`^${e}-(?:(${he})-)?\\[(.+)\\](?:(/\\w+))?(?:${d.generator.config.separators.filter(g=>g!=="-").join("|")})`)),!f.startsWith(e))return;let m=c(f)||u(f)||p(f);if(!m)return;let[$,C,k,z=""]=m;return $!==""&&se("The labeled variant is experimental and may not follow semver."),{matcher:C,handle:(g,w)=>w({...g,prefix:`${k}${r}${g.prefix}`.replace(n,"$1$2:"),sort:Si.indexOf(z)??Ci.indexOf(z)})}},multiPass:!0}}var ul=["::-webkit-resizer","::-webkit-scrollbar","::-webkit-scrollbar-button","::-webkit-scrollbar-corner","::-webkit-scrollbar-thumb","::-webkit-scrollbar-track","::-webkit-scrollbar-track-piece","::file-selector-button"],$i=Object.entries(Ee).map(([e])=>e).sort((e,t)=>t.length-e.length).join("|"),ki=Object.entries(je).map(([e])=>e).sort((e,t)=>t.length-e.length).join("|");function Ri(){let e,t;return{name:"pseudo",match(r,n){e&&e||(e=new RegExp(`^(${$i})(?:${n.generator.config.separators.join("|")})`),t=new RegExp(`^(${ki})(?:${n.generator.config.separators.filter(i=>i!=="-").join("|")})`));let o=r.match(e)||r.match(t);if(o){let i=Ee[o[1]]||je[o[1]]||`:${o[1]}`,s=Si.indexOf(o[1]);return s===-1&&(s=Ci.indexOf(o[1])),s===-1&&(s=void 0),{matcher:r.slice(o[0].length),handle:(a,c)=>{let u=i.startsWith("::")&&!ul.includes(i)?{pseudo:`${a.pseudo}${i}`}:{selector:`${a.selector}${i}`};return c({...a,...u,sort:s,noMerge:!0})}}}},multiPass:!0,autocomplete:`(${$i}|${ki}):`}}function Ti(){let e,t,r;return{match(n,o){e&&t||(e=new RegExp(`^(${he})-(${cn})(?:${o.generator.config.separators.join("|")})`),t=new RegExp(`^(${he})-(${ln})(?:${o.generator.config.separators.filter(s=>s!=="-").join("|")})`),r=new RegExp(`^(${he})-(\\[.+\\])(?:${o.generator.config.separators.filter(s=>s!=="-").join("|")})`));let i=n.match(e)||n.match(t)||n.match(r);if(i){let s=i[1],c=we(i[2],"[","]")?l.bracket(i[2]):Ee[i[2]]||je[i[2]]||`:${i[2]}`;return{matcher:n.slice(i[0].length),selector:u=>`${u}:${s}(${c})`}}},multiPass:!0,autocomplete:`(${he})-(${cn}|${ln}):`}}function Ei(e={}){let t=!!e?.attributifyPseudo,r=e?.prefix??"";r=(Array.isArray(r)?r:[r]).filter(Boolean)[0]??"";let n=(o,i)=>ll(o,t?`[${r}${o}=""]`:`.${r}${o}`,i);return[n("group"," "),n("peer","~"),n("parent",">"),n("previous","+"),n("group-aria"," "),n("peer-aria","~"),n("parent-aria",">"),n("previous-aria","+")]}var fl=/(part-\[(.+)\]:)(.+)/,ji={match(e){let t=e.match(fl);if(t){let r=`part(${t[2]})`;return{matcher:e.slice(t[1].length),selector:n=>`${n}::${r}`}}},multiPass:!0};function Tr(e){return[Zo,si,ui,li,fi,bi,xi(),wi,yi,vi,ti(),...ni,Ri(),Ti(),...Ei(e),ji,...ii(e),...ci,pi,...ri,oi,di,...ai,...Jo,mi]}var zi={position:["relative","absolute","fixed","sticky","static"],globalKeyword:S};var un=(e={})=>(e.dark=e.dark??"class",e.attributifyPseudo=e.attributifyPseudo??!1,e.preflight=e.preflight??!0,e.variablePrefix=e.variablePrefix??"un-",{name:"@unocss/preset-mini",theme:Xo,rules:To,variants:Tr(e),options:e,prefix:e.prefix,postprocess:pl(e.variablePrefix),preflights:e.preflight?dl(Gn,e.variablePrefix):[],extractorDefault:e.arbitraryVariants===!1?void 0:Hn,autocomplete:{shorthands:zi}}),Oi=un;function pl(e){if(e!=="un-")return t=>{t.entries.forEach(r=>{r[0]=r[0].replace(/^--un-/,`--${e}`),typeof r[1]=="string"&&(r[1]=r[1].replace(/var\(--un-/g,`var(--${e}`))})}}function dl(e,t){return t!=="un-"?e.map(r=>({...r,getCSS:async n=>{let o=await r.getCSS(n);if(o)return o.replace(/--un-/g,`--${t}`)}})):e}var Vi=[[/^(?:animate-)?keyframes-(.+)$/,([,e],{theme:t})=>{let r=t.animation?.keyframes?.[e];if(r)return[`@keyframes ${e}${r}`,{animation:e}]},{autocomplete:["animate-keyframes-$animation.keyframes","keyframes-$animation.keyframes"]}],[/^animate-(.+)$/,([,e],{theme:t})=>{let r=t.animation?.keyframes?.[e];if(r){let n=t.animation?.durations?.[e]??"1s",o=t.animation?.timingFns?.[e]??"linear",i=t.animation?.counts?.[e]??1,s=t.animation?.properties?.[e];return[`@keyframes ${e}${r}`,{animation:`${e} ${n} ${o} ${i}`,...s}]}return{animation:l.bracket.cssvar(e)}},{autocomplete:"animate-$animation.keyframes"}],[/^animate-name-(.+)/,([,e])=>({"animation-name":l.bracket.cssvar(e)??e})],[/^animate-duration-(.+)$/,([,e],{theme:t})=>({"animation-duration":t.duration?.[e||"DEFAULT"]??l.bracket.cssvar.time(e)}),{autocomplete:["animate-duration","animate-duration-$duration"]}],[/^animate-delay-(.+)$/,([,e],{theme:t})=>({"animation-delay":t.duration?.[e||"DEFAULT"]??l.bracket.cssvar.time(e)}),{autocomplete:["animate-delay","animate-delay-$duration"]}],[/^animate-ease(?:-(.+))?$/,([,e],{theme:t})=>({"animation-timing-function":t.easing?.[e||"DEFAULT"]??l.bracket.cssvar(e)}),{autocomplete:["animate-ease","animate-ease-$easing"]}],[/^animate-(fill-mode-|fill-|mode-)?(.+)$/,([,e,t])=>["none","forwards","backwards","both",e?S:[]].includes(t)?{"animation-fill-mode":t}:void 0,{autocomplete:["animate-(fill|mode|fill-mode)","animate-(fill|mode|fill-mode)-(none|forwards|backwards|both|inherit|initial|revert|revert-layer|unset)","animate-(none|forwards|backwards|both|inherit|initial|revert|revert-layer|unset)"]}],[/^animate-(direction-)?(.+)$/,([,e,t])=>["normal","reverse","alternate","alternate-reverse",e?S:[]].includes(t)?{"animation-direction":t}:void 0,{autocomplete:["animate-direction","animate-direction-(normal|reverse|alternate|alternate-reverse|inherit|initial|revert|revert-layer|unset)","animate-(normal|reverse|alternate|alternate-reverse|inherit|initial|revert|revert-layer|unset)"]}],[/^animate-(?:iteration-count-|iteration-|count-)(.+)$/,([,e])=>({"animation-iteration-count":l.bracket.cssvar(e)??e.replace(/-/g,",")}),{autocomplete:["animate-(iteration|count|iteration-count)","animate-(iteration|count|iteration-count)-<num>"]}],[/^animate-(play-state-|play-|state-)?(.+)$/,([,e,t])=>["paused","running",e?S:[]].includes(t)?{"animation-play-state":t}:void 0,{autocomplete:["animate-(play|state|play-state)","animate-(play|state|play-state)-(paused|running|inherit|initial|revert|revert-layer|unset)","animate-(paused|running|inherit|initial|revert|revert-layer|unset)"]}],["animate-none",{animation:"none"}],...x("animate","animation")];function Ai(e){return e?M(e,0):"rgb(255 255 255 / 0)"}function ml(e,t,r,n){return t?n!=null?M(t,n):M(t,`var(--un-${e}-opacity, ${ee(t)})`):M(r,n)}function fn(){return([,e,t],{theme:r})=>{let n=Se(t,r,"backgroundColor");if(!n)return;let{alpha:o,color:i,cssColor:s}=n;if(!i)return;let a=ml(e,s,i,o);switch(e){case"from":return{"--un-gradient-from-position":"0%","--un-gradient-from":`${a} var(--un-gradient-from-position)`,"--un-gradient-to-position":"100%","--un-gradient-to":`${Ai(s)} var(--un-gradient-to-position)`,"--un-gradient-stops":"var(--un-gradient-from), var(--un-gradient-to)"};case"via":return{"--un-gradient-via-position":"50%","--un-gradient-to":Ai(s),"--un-gradient-stops":`var(--un-gradient-from), ${a} var(--un-gradient-via-position), var(--un-gradient-to)`};case"to":return{"--un-gradient-to-position":"100%","--un-gradient-to":`${a} var(--un-gradient-to-position)`}}}}function hl(){return([,e,t])=>({[`--un-gradient-${e}-position`]:`${Number(l.bracket.cssvar.percent(t))*100}%`})}var Mi=[[/^bg-gradient-(.+)$/,([,e])=>({"--un-gradient":l.bracket(e)}),{autocomplete:["bg-gradient","bg-gradient-(from|to|via)","bg-gradient-(from|to|via)-$colors","bg-gradient-(from|to|via)-(op|opacity)","bg-gradient-(from|to|via)-(op|opacity)-<percent>"]}],[/^(?:bg-gradient-)?stops-(\[.+\])$/,([,e])=>({"--un-gradient-stops":l.bracket(e)})],[/^(?:bg-gradient-)?(from)-(.+)$/,fn()],[/^(?:bg-gradient-)?(via)-(.+)$/,fn()],[/^(?:bg-gradient-)?(to)-(.+)$/,fn()],[/^(?:bg-gradient-)?(from|via|to)-op(?:acity)?-?(.+)$/,([,e,t])=>({[`--un-${e}-opacity`]:l.bracket.percent(t)})],[/^(from|via|to)-([\d.]+)%$/,hl()],[/^bg-gradient-((?:repeating-)?(?:linear|radial|conic))$/,([,e])=>({"background-image":`${e}-gradient(var(--un-gradient, var(--un-gradient-stops, rgb(255 255 255 / 0))))`}),{autocomplete:["bg-gradient-repeating","bg-gradient-(linear|radial|conic)","bg-gradient-repeating-(linear|radial|conic)"]}],[/^bg-gradient-to-([rltb]{1,2})$/,([,e])=>{if(e in L)return{"--un-gradient-shape":`to ${L[e]}`,"--un-gradient":"var(--un-gradient-shape), var(--un-gradient-stops)","background-image":"linear-gradient(var(--un-gradient))"}},{autocomplete:`bg-gradient-to-(${Object.keys(L).filter(e=>e.length<=2&&Array.from(e).every(t=>"rltb".includes(t))).join("|")})`}],[/^(?:bg-gradient-)?shape-(.+)$/,([,e])=>{let t=e in L?`to ${L[e]}`:l.bracket(e);if(t!=null)return{"--un-gradient-shape":t,"--un-gradient":"var(--un-gradient-shape), var(--un-gradient-stops)"}},{autocomplete:["bg-gradient-shape",`bg-gradient-shape-(${Object.keys(L).join("|")})`,`shape-(${Object.keys(L).join("|")})`]}],["bg-none",{"background-image":"none"}],["box-decoration-slice",{"box-decoration-break":"slice"}],["box-decoration-clone",{"box-decoration-break":"clone"}],...x("box-decoration","box-decoration-break"),["bg-auto",{"background-size":"auto"}],["bg-cover",{"background-size":"cover"}],["bg-contain",{"background-size":"contain"}],["bg-fixed",{"background-attachment":"fixed"}],["bg-local",{"background-attachment":"local"}],["bg-scroll",{"background-attachment":"scroll"}],["bg-clip-border",{"-webkit-background-clip":"border-box","background-clip":"border-box"}],["bg-clip-content",{"-webkit-background-clip":"content-box","background-clip":"content-box"}],["bg-clip-padding",{"-webkit-background-clip":"padding-box","background-clip":"padding-box"}],["bg-clip-text",{"-webkit-background-clip":"text","background-clip":"text"}],...S.map(e=>[`bg-clip-${e}`,{"-webkit-background-clip":e,"background-clip":e}]),[/^bg-([-\w]{3,})$/,([,e])=>({"background-position":L[e]})],["bg-repeat",{"background-repeat":"repeat"}],["bg-no-repeat",{"background-repeat":"no-repeat"}],["bg-repeat-x",{"background-repeat":"repeat-x"}],["bg-repeat-y",{"background-repeat":"repeat-y"}],["bg-repeat-round",{"background-repeat":"round"}],["bg-repeat-space",{"background-repeat":"space"}],...x("bg-repeat","background-repeat"),["bg-origin-border",{"background-origin":"border-box"}],["bg-origin-padding",{"background-origin":"padding-box"}],["bg-origin-content",{"background-origin":"content-box"}],...x("bg-origin","background-origin")];var pn={disc:"disc",circle:"circle",square:"square",decimal:"decimal","zero-decimal":"decimal-leading-zero",greek:"lower-greek",roman:"lower-roman","upper-roman":"upper-roman",alpha:"lower-alpha","upper-alpha":"upper-alpha",latin:"lower-latin","upper-latin":"upper-latin"},_i=[[/^list-(.+?)(?:-(outside|inside))?$/,([,e,t])=>{let r=pn[e];if(r)return t?{"list-style-position":t,"list-style-type":r}:{"list-style-type":r}},{autocomplete:[`list-(${Object.keys(pn).join("|")})`,`list-(${Object.keys(pn).join("|")})-(outside|inside)`]}],["list-outside",{"list-style-position":"outside"}],["list-inside",{"list-style-position":"inside"}],["list-none",{"list-style-type":"none"}],[/^list-image-(.+)$/,([,e])=>{if(/^\[url\(.+\)\]$/.test(e))return{"list-style-image":l.bracket(e)}}],["list-image-none",{"list-style-image":"none"}],...x("list","list-style-type")],Pi=[[/^accent-(.+)$/,j("accent-color","accent","accentColor"),{autocomplete:"accent-$colors"}],[/^accent-op(?:acity)?-?(.+)$/,([,e])=>({"--un-accent-opacity":l.bracket.percent(e)}),{autocomplete:["accent-(op|opacity)","accent-(op|opacity)-<percent>"]}]],Fi=[[/^caret-(.+)$/,j("caret-color","caret","textColor"),{autocomplete:"caret-$colors"}],[/^caret-op(?:acity)?-?(.+)$/,([,e])=>({"--un-caret-opacity":l.bracket.percent(e)}),{autocomplete:["caret-(op|opacity)","caret-(op|opacity)-<percent>"]}]],Li=[["image-render-auto",{"image-rendering":"auto"}],["image-render-edge",{"image-rendering":"crisp-edges"}],["image-render-pixel",[["-ms-interpolation-mode","nearest-neighbor"],["image-rendering","-webkit-optimize-contrast"],["image-rendering","-moz-crisp-edges"],["image-rendering","-o-pixelated"],["image-rendering","pixelated"]]]],Ui=[["overscroll-auto",{"overscroll-behavior":"auto"}],["overscroll-contain",{"overscroll-behavior":"contain"}],["overscroll-none",{"overscroll-behavior":"none"}],...x("overscroll","overscroll-behavior"),["overscroll-x-auto",{"overscroll-behavior-x":"auto"}],["overscroll-x-contain",{"overscroll-behavior-x":"contain"}],["overscroll-x-none",{"overscroll-behavior-x":"none"}],...x("overscroll-x","overscroll-behavior-x"),["overscroll-y-auto",{"overscroll-behavior-y":"auto"}],["overscroll-y-contain",{"overscroll-behavior-y":"contain"}],["overscroll-y-none",{"overscroll-behavior-y":"none"}],...x("overscroll-y","overscroll-behavior-y")],Wi=[["scroll-auto",{"scroll-behavior":"auto"}],["scroll-smooth",{"scroll-behavior":"smooth"}],...x("scroll","scroll-behavior")];var Ni=[[/^columns-(.+)$/,([,e])=>({columns:l.bracket.global.number.auto.numberWithUnit(e)}),{autocomplete:"columns-<num>"}],["break-before-auto",{"break-before":"auto"}],["break-before-avoid",{"break-before":"avoid"}],["break-before-all",{"break-before":"all"}],["break-before-avoid-page",{"break-before":"avoid-page"}],["break-before-page",{"break-before":"page"}],["break-before-left",{"break-before":"left"}],["break-before-right",{"break-before":"right"}],["break-before-column",{"break-before":"column"}],...x("break-before"),["break-inside-auto",{"break-inside":"auto"}],["break-inside-avoid",{"break-inside":"avoid"}],["break-inside-avoid-page",{"break-inside":"avoid-page"}],["break-inside-avoid-column",{"break-inside":"avoid-column"}],...x("break-inside"),["break-after-auto",{"break-after":"auto"}],["break-after-avoid",{"break-after":"avoid"}],["break-after-all",{"break-after":"all"}],["break-after-avoid-page",{"break-after":"avoid-page"}],["break-after-page",{"break-after":"page"}],["break-after-left",{"break-after":"left"}],["break-after-right",{"break-after":"right"}],["break-after-column",{"break-after":"column"}],...x("break-after")];var gl=/@media \(min-width: (.+)\)/,Bi=[[/^__container$/,(e,t)=>{let{theme:r,variantHandlers:n}=t,o=r.container?.padding,i;V(o)?i=o:i=o?.DEFAULT;let s=r.container?.maxWidth,a;for(let u of n){let p=u.handle?.({},f=>f)?.parent;if(V(p)){let f=p.match(gl)?.[1];if(f){let m=(pe(t)??[]).find($=>$.size===f)?.point;s?m&&(a=s?.[m]):a=f,m&&!V(o)&&(i=o?.[m]??i)}}}let c={"max-width":a};return n.length||(c.width="100%"),r.container?.center&&(c["margin-left"]="auto",c["margin-right"]="auto"),o&&(c["padding-left"]=i,c["padding-right"]=i),c},{internal:!0}]],Di=[[/^(?:(\w+)[:-])?container$/,([,e],t)=>{let r=(pe(t)??[]).map(o=>o.point);if(e){if(!r.includes(e))return;r=r.slice(r.indexOf(e))}let n=r.map(o=>`${o}:__container`);return e||n.unshift("__container"),n}]];var Ii={"--un-blur":E,"--un-brightness":E,"--un-contrast":E,"--un-drop-shadow":E,"--un-grayscale":E,"--un-hue-rotate":E,"--un-invert":E,"--un-saturate":E,"--un-sepia":E},Er="var(--un-blur) var(--un-brightness) var(--un-contrast) var(--un-drop-shadow) var(--un-grayscale) var(--un-hue-rotate) var(--un-invert) var(--un-saturate) var(--un-sepia)",Ki={"--un-backdrop-blur":E,"--un-backdrop-brightness":E,"--un-backdrop-contrast":E,"--un-backdrop-grayscale":E,"--un-backdrop-hue-rotate":E,"--un-backdrop-invert":E,"--un-backdrop-opacity":E,"--un-backdrop-saturate":E,"--un-backdrop-sepia":E},jr="var(--un-backdrop-blur) var(--un-backdrop-brightness) var(--un-backdrop-contrast) var(--un-backdrop-grayscale) var(--un-backdrop-hue-rotate) var(--un-backdrop-invert) var(--un-backdrop-opacity) var(--un-backdrop-saturate) var(--un-backdrop-sepia)";function dn(e){let t=l.bracket.cssvar(e||"");if(t!=null||(t=e?l.percent(e):"1",t!=null&&Number.parseFloat(t)<=1))return t}function te(e,t){return([,r,n],{theme:o})=>{let i=t(n,o)??(n==="none"?"0":"");if(i!=="")return r?{[`--un-${r}${e}`]:`${e}(${i})`,"-webkit-backdrop-filter":jr,"backdrop-filter":jr}:{[`--un-${e}`]:`${e}(${i})`,filter:Er}}}function bl([,e],{theme:t}){let r=t.dropShadow?.[e||"DEFAULT"];if(r!=null)return{"--un-drop-shadow":`drop-shadow(${Ce(r,"--un-drop-shadow-color").join(") drop-shadow(")})`,filter:Er};if(r=l.bracket.cssvar(e),r!=null)return{"--un-drop-shadow":`drop-shadow(${r})`,filter:Er}}var Hi=[[/^(?:(backdrop-)|filter-)?blur(?:-(.+))?$/,te("blur",(e,t)=>t.blur?.[e||"DEFAULT"]||l.bracket.cssvar.px(e)),{autocomplete:["(backdrop|filter)-blur-$blur","blur-$blur","filter-blur"]}],[/^(?:(backdrop-)|filter-)?brightness-(.+)$/,te("brightness",e=>l.bracket.cssvar.percent(e)),{autocomplete:["(backdrop|filter)-brightness-<percent>","brightness-<percent>"]}],[/^(?:(backdrop-)|filter-)?contrast-(.+)$/,te("contrast",e=>l.bracket.cssvar.percent(e)),{autocomplete:["(backdrop|filter)-contrast-<percent>","contrast-<percent>"]}],[/^(?:filter-)?drop-shadow(?:-(.+))?$/,bl,{autocomplete:["filter-drop","filter-drop-shadow","filter-drop-shadow-color","drop-shadow","drop-shadow-color","filter-drop-shadow-$dropShadow","drop-shadow-$dropShadow","filter-drop-shadow-color-$colors","drop-shadow-color-$colors","filter-drop-shadow-color-(op|opacity)","drop-shadow-color-(op|opacity)","filter-drop-shadow-color-(op|opacity)-<percent>","drop-shadow-color-(op|opacity)-<percent>"]}],[/^(?:filter-)?drop-shadow-color-(.+)$/,j("--un-drop-shadow-color","drop-shadow","shadowColor")],[/^(?:filter-)?drop-shadow-color-op(?:acity)?-?(.+)$/,([,e])=>({"--un-drop-shadow-opacity":l.bracket.percent(e)})],[/^(?:(backdrop-)|filter-)?grayscale(?:-(.+))?$/,te("grayscale",dn),{autocomplete:["(backdrop|filter)-grayscale","(backdrop|filter)-grayscale-<percent>","grayscale-<percent>"]}],[/^(?:(backdrop-)|filter-)?hue-rotate-(.+)$/,te("hue-rotate",e=>l.bracket.cssvar.degree(e))],[/^(?:(backdrop-)|filter-)?invert(?:-(.+))?$/,te("invert",dn),{autocomplete:["(backdrop|filter)-invert","(backdrop|filter)-invert-<percent>","invert-<percent>"]}],[/^(backdrop-)op(?:acity)?-(.+)$/,te("opacity",e=>l.bracket.cssvar.percent(e)),{autocomplete:["backdrop-(op|opacity)","backdrop-(op|opacity)-<percent>"]}],[/^(?:(backdrop-)|filter-)?saturate-(.+)$/,te("saturate",e=>l.bracket.cssvar.percent(e)),{autocomplete:["(backdrop|filter)-saturate","(backdrop|filter)-saturate-<percent>","saturate-<percent>"]}],[/^(?:(backdrop-)|filter-)?sepia(?:-(.+))?$/,te("sepia",dn),{autocomplete:["(backdrop|filter)-sepia","(backdrop|filter)-sepia-<percent>","sepia-<percent>"]}],["filter",{filter:Er}],["backdrop-filter",{"-webkit-backdrop-filter":jr,"backdrop-filter":jr}],["filter-none",{filter:"none"}],["backdrop-filter-none",{"-webkit-backdrop-filter":"none","backdrop-filter":"none"}],...S.map(e=>[`filter-${e}`,{filter:e}]),...S.map(e=>[`backdrop-filter-${e}`,{"-webkit-backdrop-filter":e,"backdrop-filter":e}])];var qi=[[/^space-([xy])-(.+)$/,Gi,{autocomplete:["space-(x|y|block|inline)","space-(x|y|block|inline)-reverse","space-(x|y|block|inline)-$spacing"]}],[/^space-([xy])-reverse$/,([,e])=>({[`--un-space-${e}-reverse`]:1})],[/^space-(block|inline)-(.+)$/,Gi],[/^space-(block|inline)-reverse$/,([,e])=>({[`--un-space-${e}-reverse`]:1})]];function Gi([,e,t],{theme:r}){let n=r.spacing?.[t||"DEFAULT"]??l.bracket.cssvar.auto.fraction.rem(t||"1");if(n!=null){n==="0"&&(n="0px");let o=U[e].map(i=>{let s=`margin${i}`,a=i.endsWith("right")||i.endsWith("bottom")?`calc(${n} * var(--un-space-${e}-reverse))`:`calc(${n} * calc(1 - var(--un-space-${e}-reverse)))`;return[s,a]});if(o)return[[`--un-space-${e}-reverse`,0],...o]}}var Yi=[["uppercase",{"text-transform":"uppercase"}],["lowercase",{"text-transform":"lowercase"}],["capitalize",{"text-transform":"capitalize"}],["normal-case",{"text-transform":"none"}]],Xi=[...["manual","auto","none",...S].map(e=>[`hyphens-${e}`,{"-webkit-hyphens":e,"-ms-hyphens":e,hyphens:e}])],Zi=[["write-vertical-right",{"writing-mode":"vertical-rl"}],["write-vertical-left",{"writing-mode":"vertical-lr"}],["write-normal",{"writing-mode":"horizontal-tb"}],...x("write","writing-mode")],Ji=[["write-orient-mixed",{"text-orientation":"mixed"}],["write-orient-sideways",{"text-orientation":"sideways"}],["write-orient-upright",{"text-orientation":"upright"}],...x("write-orient","text-orientation")],Qi=[["sr-only",{position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0,0,0,0)","white-space":"nowrap","border-width":0}],["not-sr-only",{position:"static",width:"auto",height:"auto",padding:"0",margin:"0",overflow:"visible",clip:"auto","white-space":"normal"}]],es=[["isolate",{isolation:"isolate"}],["isolate-auto",{isolation:"auto"}],["isolation-auto",{isolation:"auto"}]],ts=[["object-cover",{"object-fit":"cover"}],["object-contain",{"object-fit":"contain"}],["object-fill",{"object-fit":"fill"}],["object-scale-down",{"object-fit":"scale-down"}],["object-none",{"object-fit":"none"}],[/^object-(.+)$/,([,e])=>{if(L[e])return{"object-position":L[e]};if(l.bracketOfPosition(e)!=null)return{"object-position":l.bracketOfPosition(e).split(" ").map(t=>l.position.fraction.auto.px.cssvar(t)??t).join(" ")}},{autocomplete:`object-(${Object.keys(L).join("|")})`}]],rs=[["bg-blend-multiply",{"background-blend-mode":"multiply"}],["bg-blend-screen",{"background-blend-mode":"screen"}],["bg-blend-overlay",{"background-blend-mode":"overlay"}],["bg-blend-darken",{"background-blend-mode":"darken"}],["bg-blend-lighten",{"background-blend-mode":"lighten"}],["bg-blend-color-dodge",{"background-blend-mode":"color-dodge"}],["bg-blend-color-burn",{"background-blend-mode":"color-burn"}],["bg-blend-hard-light",{"background-blend-mode":"hard-light"}],["bg-blend-soft-light",{"background-blend-mode":"soft-light"}],["bg-blend-difference",{"background-blend-mode":"difference"}],["bg-blend-exclusion",{"background-blend-mode":"exclusion"}],["bg-blend-hue",{"background-blend-mode":"hue"}],["bg-blend-saturation",{"background-blend-mode":"saturation"}],["bg-blend-color",{"background-blend-mode":"color"}],["bg-blend-luminosity",{"background-blend-mode":"luminosity"}],["bg-blend-normal",{"background-blend-mode":"normal"}],...x("bg-blend","background-blend")],ns=[["mix-blend-multiply",{"mix-blend-mode":"multiply"}],["mix-blend-screen",{"mix-blend-mode":"screen"}],["mix-blend-overlay",{"mix-blend-mode":"overlay"}],["mix-blend-darken",{"mix-blend-mode":"darken"}],["mix-blend-lighten",{"mix-blend-mode":"lighten"}],["mix-blend-color-dodge",{"mix-blend-mode":"color-dodge"}],["mix-blend-color-burn",{"mix-blend-mode":"color-burn"}],["mix-blend-hard-light",{"mix-blend-mode":"hard-light"}],["mix-blend-soft-light",{"mix-blend-mode":"soft-light"}],["mix-blend-difference",{"mix-blend-mode":"difference"}],["mix-blend-exclusion",{"mix-blend-mode":"exclusion"}],["mix-blend-hue",{"mix-blend-mode":"hue"}],["mix-blend-saturation",{"mix-blend-mode":"saturation"}],["mix-blend-color",{"mix-blend-mode":"color"}],["mix-blend-luminosity",{"mix-blend-mode":"luminosity"}],["mix-blend-plus-lighter",{"mix-blend-mode":"plus-lighter"}],["mix-blend-normal",{"mix-blend-mode":"normal"}],...x("mix-blend")],os=[["min-h-dvh",{"min-height":"100dvh"}],["min-h-svh",{"min-height":"100svh"}],["min-h-lvh",{"min-height":"100lvh"}],["h-dvh",{height:"100dvh"}],["h-svh",{height:"100svh"}],["h-lvh",{height:"100lvh"}],["max-h-dvh",{"max-height":"100dvh"}],["max-h-svh",{"max-height":"100svh"}],["max-h-lvh",{"max-height":"100lvh"}]];var ss={"--un-border-spacing-x":0,"--un-border-spacing-y":0},is="var(--un-border-spacing-x) var(--un-border-spacing-y)",as=[["inline-table",{display:"inline-table"}],["table",{display:"table"}],["table-caption",{display:"table-caption"}],["table-cell",{display:"table-cell"}],["table-column",{display:"table-column"}],["table-column-group",{display:"table-column-group"}],["table-footer-group",{display:"table-footer-group"}],["table-header-group",{display:"table-header-group"}],["table-row",{display:"table-row"}],["table-row-group",{display:"table-row-group"}],["border-collapse",{"border-collapse":"collapse"}],["border-separate",{"border-collapse":"separate"}],[/^border-spacing-(.+)$/,([,e],{theme:t})=>{let r=t.spacing?.[e]??l.bracket.cssvar.global.auto.fraction.rem(e);if(r!=null)return{"--un-border-spacing-x":r,"--un-border-spacing-y":r,"border-spacing":is}},{autocomplete:["border-spacing","border-spacing-$spacing"]}],[/^border-spacing-([xy])-(.+)$/,([,e,t],{theme:r})=>{let n=r.spacing?.[t]??l.bracket.cssvar.global.auto.fraction.rem(t);if(n!=null)return{[`--un-border-spacing-${e}`]:n,"border-spacing":is}},{autocomplete:["border-spacing-(x|y)","border-spacing-(x|y)-$spacing"]}],["caption-top",{"caption-side":"top"}],["caption-bottom",{"caption-side":"bottom"}],["table-auto",{"table-layout":"auto"}],["table-fixed",{"table-layout":"fixed"}],["table-empty-cells-visible",{"empty-cells":"show"}],["table-empty-cells-hidden",{"empty-cells":"hide"}]];var xl={"bg-blend":"background-blend-mode","bg-clip":"-webkit-background-clip","bg-gradient":"linear-gradient","bg-image":"background-image","bg-origin":"background-origin","bg-position":"background-position","bg-repeat":"background-repeat","bg-size":"background-size","mix-blend":"mix-blend-mode",object:"object-fit","object-position":"object-position",write:"writing-mode","write-orient":"text-orientation"},cs=[[/^(.+?)-(\$.+)$/,([,e,t])=>{let r=xl[e];if(r)return{[r]:l.cssvar(t)}}]];var ls=[[/^divide-?([xy])$/,zr,{autocomplete:["divide-(x|y|block|inline)","divide-(x|y|block|inline)-reverse","divide-(x|y|block|inline)-$lineWidth"]}],[/^divide-?([xy])-?(.+)$/,zr],[/^divide-?([xy])-reverse$/,([,e])=>({[`--un-divide-${e}-reverse`]:1})],[/^divide-(block|inline)$/,zr],[/^divide-(block|inline)-(.+)$/,zr],[/^divide-(block|inline)-reverse$/,([,e])=>({[`--un-divide-${e}-reverse`]:1})],[/^divide-(.+)$/,j("border-color","divide","borderColor"),{autocomplete:"divide-$colors"}],[/^divide-op(?:acity)?-?(.+)$/,([,e])=>({"--un-divide-opacity":l.bracket.percent(e)}),{autocomplete:["divide-(op|opacity)","divide-(op|opacity)-<percent>"]}],...de.map(e=>[`divide-${e}`,{"border-style":e}])];function zr([,e,t],{theme:r}){let n=r.lineWidth?.[t||"DEFAULT"]??l.bracket.cssvar.px(t||"1");if(n!=null){n==="0"&&(n="0px");let o=U[e].map(i=>{let s=`border${i}-width`,a=i.endsWith("right")||i.endsWith("bottom")?`calc(${n} * var(--un-divide-${e}-reverse))`:`calc(${n} * calc(1 - var(--un-divide-${e}-reverse)))`;return[s,a]});if(o)return[[`--un-divide-${e}-reverse`,0],...o]}}var us=[[/^line-clamp-(\d+)$/,([,e])=>({overflow:"hidden",display:"-webkit-box","-webkit-box-orient":"vertical","-webkit-line-clamp":e,"line-clamp":e}),{autocomplete:["line-clamp","line-clamp-<num>"]}],...["none",...S].map(e=>[`line-clamp-${e}`,{overflow:"visible",display:"block","-webkit-box-orient":"horizontal","-webkit-line-clamp":e,"line-clamp":e}])];var fs={"--un-ordinal":E,"--un-slashed-zero":E,"--un-numeric-figure":E,"--un-numeric-spacing":E,"--un-numeric-fraction":E};function le(e){return{...e,"font-variant-numeric":"var(--un-ordinal) var(--un-slashed-zero) var(--un-numeric-figure) var(--un-numeric-spacing) var(--un-numeric-fraction)"}}var ps=[[/^ordinal$/,()=>le({"--un-ordinal":"ordinal"}),{autocomplete:"ordinal"}],[/^slashed-zero$/,()=>le({"--un-slashed-zero":"slashed-zero"}),{autocomplete:"slashed-zero"}],[/^lining-nums$/,()=>le({"--un-numeric-figure":"lining-nums"}),{autocomplete:"lining-nums"}],[/^oldstyle-nums$/,()=>le({"--un-numeric-figure":"oldstyle-nums"}),{autocomplete:"oldstyle-nums"}],[/^proportional-nums$/,()=>le({"--un-numeric-spacing":"proportional-nums"}),{autocomplete:"proportional-nums"}],[/^tabular-nums$/,()=>le({"--un-numeric-spacing":"tabular-nums"}),{autocomplete:"tabular-nums"}],[/^diagonal-fractions$/,()=>le({"--un-numeric-fraction":"diagonal-fractions"}),{autocomplete:"diagonal-fractions"}],[/^stacked-fractions$/,()=>le({"--un-numeric-fraction":"stacked-fractions"}),{autocomplete:"stacked-fractions"}],["normal-nums",{"font-variant-numeric":"normal"}]];var ds={"--un-pan-x":E,"--un-pan-y":E,"--un-pinch-zoom":E},mn="var(--un-pan-x) var(--un-pan-y) var(--un-pinch-zoom)",ms=[[/^touch-pan-(x|left|right)$/,([,e])=>({"--un-pan-x":`pan-${e}`,"touch-action":mn}),{autocomplete:["touch-pan","touch-pan-(x|left|right|y|up|down)"]}],[/^touch-pan-(y|up|down)$/,([,e])=>({"--un-pan-y":`pan-${e}`,"touch-action":mn})],["touch-pinch-zoom",{"--un-pinch-zoom":"pinch-zoom","touch-action":mn}],["touch-auto",{"touch-action":"auto"}],["touch-manipulation",{"touch-action":"manipulation"}],["touch-none",{"touch-action":"none"}],...x("touch","touch-action")];var hs={"--un-scroll-snap-strictness":"proximity"},gs=[[/^snap-(x|y)$/,([,e])=>({"scroll-snap-type":`${e} var(--un-scroll-snap-strictness)`}),{autocomplete:"snap-(x|y|both)"}],[/^snap-both$/,()=>({"scroll-snap-type":"both var(--un-scroll-snap-strictness)"})],["snap-mandatory",{"--un-scroll-snap-strictness":"mandatory"}],["snap-proximity",{"--un-scroll-snap-strictness":"proximity"}],["snap-none",{"scroll-snap-type":"none"}],["snap-start",{"scroll-snap-align":"start"}],["snap-end",{"scroll-snap-align":"end"}],["snap-center",{"scroll-snap-align":"center"}],["snap-align-none",{"scroll-snap-align":"none"}],["snap-normal",{"scroll-snap-stop":"normal"}],["snap-always",{"scroll-snap-stop":"always"}],[/^scroll-ma?()-?(.+)$/,_("scroll-margin"),{autocomplete:["scroll-(m|p|ma|pa|block|inline)","scroll-(m|p|ma|pa|block|inline)-$spacing","scroll-(m|p|ma|pa|block|inline)-(x|y|r|l|t|b|bs|be|is|ie)","scroll-(m|p|ma|pa|block|inline)-(x|y|r|l|t|b|bs|be|is|ie)-$spacing"]}],[/^scroll-m-?([xy])-?(.+)$/,_("scroll-margin")],[/^scroll-m-?([rltb])-?(.+)$/,_("scroll-margin")],[/^scroll-m-(block|inline)-(.+)$/,_("scroll-margin")],[/^scroll-m-?([bi][se])-?(.+)$/,_("scroll-margin")],[/^scroll-pa?()-?(.+)$/,_("scroll-padding")],[/^scroll-p-?([xy])-?(.+)$/,_("scroll-padding")],[/^scroll-p-?([rltb])-?(.+)$/,_("scroll-padding")],[/^scroll-p-(block|inline)-(.+)$/,_("scroll-padding")],[/^scroll-p-?([bi][se])-?(.+)$/,_("scroll-padding")]];var bs=[[/^\$ placeholder-(.+)$/,j("color","placeholder","accentColor"),{autocomplete:"placeholder-$colors"}],[/^\$ placeholder-op(?:acity)?-?(.+)$/,([,e])=>({"--un-placeholder-opacity":l.bracket.percent(e)}),{autocomplete:["placeholder-(op|opacity)","placeholder-(op|opacity)-<percent>"]}]];var xs=[[/^view-transition-([\w-]+)$/,([,e])=>({"view-transition-name":e})]];var ys=[wr,cs,$r,Bi,Xt,Qi,Zt,qt,Wt,Dt,us,es,Kt,Nt,Ft,It,br,Ht,Gt,hr,mr,zt,as,vr,Vi,Yt,ms,Qt,Jt,gs,_i,wt,Ni,Ke,Ie,De,Pt,Bt,qi,ls,Ut,Ui,Wi,ir,er,nr,kt,Ct,Mi,Sr,ts,gr,bt,At,or,gt,Ot,sr,Yi,ar,ps,Tt,cr,Vt,Mt,_t,Xi,Zi,Ji,Fi,Pi,St,rs,ns,pr,vt,ur,Li,Hi,jt,$t,tr,rr,bs,Rt,xs,os,kr].flat(1);var vs=[...Di];var hn={inherit:"inherit",current:"currentColor",transparent:"transparent",black:"#000",white:"#fff",rose:{50:"#fff1f2",100:"#ffe4e6",200:"#fecdd3",300:"#fda4af",400:"#fb7185",500:"#f43f5e",600:"#e11d48",700:"#be123c",800:"#9f1239",900:"#881337",950:"#4c0519"},pink:{50:"#fdf2f8",100:"#fce7f3",200:"#fbcfe8",300:"#f9a8d4",400:"#f472b6",500:"#ec4899",600:"#db2777",700:"#be185d",800:"#9d174d",900:"#831843",950:"#500724"},fuchsia:{50:"#fdf4ff",100:"#fae8ff",200:"#f5d0fe",300:"#f0abfc",400:"#e879f9",500:"#d946ef",600:"#c026d3",700:"#a21caf",800:"#86198f",900:"#701a75",950:"#4a044e"},purple:{50:"#faf5ff",100:"#f3e8ff",200:"#e9d5ff",300:"#d8b4fe",400:"#c084fc",500:"#a855f7",600:"#9333ea",700:"#7e22ce",800:"#6b21a8",900:"#581c87",950:"#3b0764"},violet:{50:"#f5f3ff",100:"#ede9fe",200:"#ddd6fe",300:"#c4b5fd",400:"#a78bfa",500:"#8b5cf6",600:"#7c3aed",700:"#6d28d9",800:"#5b21b6",900:"#4c1d95",950:"#2e1065"},indigo:{50:"#eef2ff",100:"#e0e7ff",200:"#c7d2fe",300:"#a5b4fc",400:"#818cf8",500:"#6366f1",600:"#4f46e5",700:"#4338ca",800:"#3730a3",900:"#312e81",950:"#1e1b4b"},blue:{50:"#eff6ff",100:"#dbeafe",200:"#bfdbfe",300:"#93c5fd",400:"#60a5fa",500:"#3b82f6",600:"#2563eb",700:"#1d4ed8",800:"#1e40af",900:"#1e3a8a",950:"#172554"},sky:{50:"#f0f9ff",100:"#e0f2fe",200:"#bae6fd",300:"#7dd3fc",400:"#38bdf8",500:"#0ea5e9",600:"#0284c7",700:"#0369a1",800:"#075985",900:"#0c4a6e",950:"#082f49"},cyan:{50:"#ecfeff",100:"#cffafe",200:"#a5f3fc",300:"#67e8f9",400:"#22d3ee",500:"#06b6d4",600:"#0891b2",700:"#0e7490",800:"#155e75",900:"#164e63",950:"#083344"},teal:{50:"#f0fdfa",100:"#ccfbf1",200:"#99f6e4",300:"#5eead4",400:"#2dd4bf",500:"#14b8a6",600:"#0d9488",700:"#0f766e",800:"#115e59",900:"#134e4a",950:"#042f2e"},emerald:{50:"#ecfdf5",100:"#d1fae5",200:"#a7f3d0",300:"#6ee7b7",400:"#34d399",500:"#10b981",600:"#059669",700:"#047857",800:"#065f46",900:"#064e3b",950:"#022c22"},green:{50:"#f0fdf4",100:"#dcfce7",200:"#bbf7d0",300:"#86efac",400:"#4ade80",500:"#22c55e",600:"#16a34a",700:"#15803d",800:"#166534",900:"#14532d",950:"#052e16"},lime:{50:"#f7fee7",100:"#ecfccb",200:"#d9f99d",300:"#bef264",400:"#a3e635",500:"#84cc16",600:"#65a30d",700:"#4d7c0f",800:"#3f6212",900:"#365314",950:"#1a2e05"},yellow:{50:"#fefce8",100:"#fef9c3",200:"#fef08a",300:"#fde047",400:"#facc15",500:"#eab308",600:"#ca8a04",700:"#a16207",800:"#854d0e",900:"#713f12",950:"#422006"},amber:{50:"#fffbeb",100:"#fef3c7",200:"#fde68a",300:"#fcd34d",400:"#fbbf24",500:"#f59e0b",600:"#d97706",700:"#b45309",800:"#92400e",900:"#78350f",950:"#451a03"},orange:{50:"#fff7ed",100:"#ffedd5",200:"#fed7aa",300:"#fdba74",400:"#fb923c",500:"#f97316",600:"#ea580c",700:"#c2410c",800:"#9a3412",900:"#7c2d12",950:"#431407"},red:{50:"#fef2f2",100:"#fee2e2",200:"#fecaca",300:"#fca5a5",400:"#f87171",500:"#ef4444",600:"#dc2626",700:"#b91c1c",800:"#991b1b",900:"#7f1d1d",950:"#450a0a"},gray:{50:"#f9fafb",100:"#f3f4f6",200:"#e5e7eb",300:"#d1d5db",400:"#9ca3af",500:"#6b7280",600:"#4b5563",700:"#374151",800:"#1f2937",900:"#111827",950:"#030712"},slate:{50:"#f8fafc",100:"#f1f5f9",200:"#e2e8f0",300:"#cbd5e1",400:"#94a3b8",500:"#64748b",600:"#475569",700:"#334155",800:"#1e293b",900:"#0f172a",950:"#020617"},zinc:{50:"#fafafa",100:"#f4f4f5",200:"#e4e4e7",300:"#d4d4d8",400:"#a1a1aa",500:"#71717a",600:"#52525b",700:"#3f3f46",800:"#27272a",900:"#18181b",950:"#09090b"},neutral:{50:"#fafafa",100:"#f5f5f5",200:"#e5e5e5",300:"#d4d4d4",400:"#a3a3a3",500:"#737373",600:"#525252",700:"#404040",800:"#262626",900:"#171717",950:"#0a0a0a"},stone:{50:"#fafaf9",100:"#f5f5f4",200:"#e7e5e4",300:"#d6d3d1",400:"#a8a29e",500:"#78716c",600:"#57534e",700:"#44403c",800:"#292524",900:"#1c1917",950:"#0c0a09"},light:{50:"#fdfdfd",100:"#fcfcfc",200:"#fafafa",300:"#f8f9fa",400:"#f6f6f6",500:"#f2f2f2",600:"#f1f3f5",700:"#e9ecef",800:"#dee2e6",900:"#dde1e3",950:"#d8dcdf"},dark:{50:"#4a4a4a",100:"#3c3c3c",200:"#323232",300:"#2d2d2d",400:"#222222",500:"#1f1f1f",600:"#1c1c1e",700:"#1b1b1b",800:"#181818",900:"#0f0f0f",950:"#080808"},get lightblue(){return this.sky},get lightBlue(){return this.sky},get warmgray(){return this.stone},get warmGray(){return this.stone},get truegray(){return this.neutral},get trueGray(){return this.neutral},get coolgray(){return this.gray},get coolGray(){return this.gray},get bluegray(){return this.slate},get blueGray(){return this.slate}};Object.values(hn).forEach(e=>{typeof e!="string"&&e!==void 0&&(e.DEFAULT=e.DEFAULT||e[400],Object.keys(e).forEach(t=>{let r=+t/100;r===Math.round(r)&&(e[r]=e[t])}))});var yl={l:["-left"],r:["-right"],t:["-top"],b:["-bottom"],s:["-inline-start"],e:["-inline-end"],x:["-left","-right"],y:["-top","-bottom"],"":[""],bs:["-block-start"],be:["-block-end"],is:["-inline-start"],ie:["-inline-end"],block:["-block-start","-block-end"],inline:["-inline-start","-inline-end"]},Zm={...yl,s:["-inset-inline-start"],start:["-inset-inline-start"],e:["-inset-inline-end"],end:["-inset-inline-end"],bs:["-inset-block-start"],be:["-inset-block-end"],is:["-inset-inline-start"],ie:["-inset-inline-end"],block:["-inset-block-start","-inset-block-end"],inline:["-inset-inline-start","-inset-inline-end"]};var vl={x:["-x"],y:["-y"],z:["-z"],"":["-x","-y"]},wl=["x","y","z"],ws=["top","top center","top left","top right","bottom","bottom center","bottom left","bottom right","left","left center","left top","left bottom","right","right center","right top","right bottom","center","center top","center bottom","center left","center right","center center"],Xe=Object.assign({},...ws.map(e=>({[e.replace(/ /,"-")]:e})),...ws.map(e=>({[e.replace(/\b(\w)\w+/g,"$1").replace(/ /,"")]:e}))),gn=["inherit","initial","revert","revert-layer","unset"],$l=/^(calc|clamp|min|max)\s*\((.+)\)(.*)/;var bn=/^(-?\d*(?:\.\d+)?)(px|pt|pc|%|r?(?:em|ex|lh|cap|ch|ic)|(?:[sld]?v|cq)(?:[whib]|min|max)|in|cm|mm|rpx)?$/i,ks=/^(-?\d*(?:\.\d+)?)$/,Ss=/^(px|[sld]?v[wh])$/i,Cs={px:1,vw:100,vh:100,svw:100,svh:100,dvw:100,dvh:100,lvh:100,lvw:100},Rs=/^\[(color|length|size|position|quoted|string):/i,kl=/,(?![^()]*\))/g,Sl=["color","border-color","background-color","flex-grow","flex","flex-shrink","caret-color","font","gap","opacity","visibility","z-index","font-weight","zoom","text-shadow","transform","box-shadow","background-position","left","right","top","bottom","object-position","max-height","min-height","max-width","min-width","height","width","border-width","margin","padding","outline-width","outline-offset","font-size","line-height","text-indent","vertical-align","border-spacing","letter-spacing","word-spacing","stroke","filter","backdrop-filter","fill","mask","mask-size","mask-border","clip-path","clip","border-radius"];function Y(e){return+e.toFixed(10)}function Cl(e){let t=e.match(bn);if(!t)return;let[,r,n]=t,o=Number.parseFloat(r);if(n&&!Number.isNaN(o))return`${Y(o)}${n}`}function Rl(e){if(e==="auto"||e==="a")return"auto"}function Tl(e){if(!e)return;if(Ss.test(e))return`${Cs[e]}${e}`;let t=e.match(bn);if(!t)return;let[,r,n]=t,o=Number.parseFloat(r);if(!Number.isNaN(o))return o===0?"0":n?`${Y(o)}${n}`:`${Y(o/4)}rem`}function El(e){if(Ss.test(e))return`${Cs[e]}${e}`;let t=e.match(bn);if(!t)return;let[,r,n]=t,o=Number.parseFloat(r);if(!Number.isNaN(o))return n?`${Y(o)}${n}`:`${Y(o)}px`}function jl(e){if(!ks.test(e))return;let t=Number.parseFloat(e);if(!Number.isNaN(t))return Y(t)}function zl(e){if(e.endsWith("%")&&(e=e.slice(0,-1)),!ks.test(e))return;let t=Number.parseFloat(e);if(!Number.isNaN(t))return`${Y(t/100)}`}function Ol(e){if(!e)return;if(e==="full")return"100%";let[t,r]=e.split("/"),n=Number.parseFloat(t)/Number.parseFloat(r);if(!Number.isNaN(n))return n===0?"0":`${Y(n*100)}%`}function Vr(e,t){if(e&&e.startsWith("[")&&e.endsWith("]")){let r,n,o=e.match(Rs);if(o?(t||(n=o[1]),r=e.slice(o[0].length,-1)):r=e.slice(1,-1),!r||r==='=""')return;r.startsWith("--")&&(r=`var(${r})`);let i=0;for(let s of r)if(s==="[")i+=1;else if(s==="]"&&(i-=1,i<0))return;if(i)return;switch(n){case"string":return r.replace(/(^|[^\\])_/g,"$1 ").replace(/\\_/g,"_");case"quoted":return r.replace(/(^|[^\\])_/g,"$1 ").replace(/\\_/g,"_").replace(/(["\\])/g,"\\$1").replace(/^(.+)$/,'"$1"')}return r.replace(/(url\(.*?\))/g,s=>s.replace(/_/g,"\\_")).replace(/(^|[^\\])_/g,"$1 ").replace(/\\_/g,"_").replace(/(?:calc|clamp|max|min)\((.*)/g,s=>{let a=[];return s.replace(/var\((--.+?)[,)]/g,(c,u)=>(a.push(u),c.replace(u,"--un-calc"))).replace(/(-?\d*\.?\d(?!-\d.+[,)](?![^+\-/*])\D)(?:%|[a-z]+)?|\))([+\-/*])/g,"$1 $2 ").replace(/--un-calc/g,()=>a.shift())})}}function Vl(e){return Vr(e)}function Al(e){return Vr(e,"color")}function Ml(e){return Vr(e,"length")}function _l(e){return Vr(e,"position")}function Pl(e){if(/^\$[^\s'"`;{}]/.test(e)){let[t,r]=e.slice(1).split(",");return`var(--${Q(t)}${r?`, ${r}`:""})`}}function Fl(e){let t=e.match(/^(-?[0-9.]+)(s|ms)?$/i);if(!t)return;let[,r,n]=t,o=Number.parseFloat(r);if(!Number.isNaN(o))return o===0&&!n?"0s":n?`${Y(o)}${n}`:`${Y(o)}ms`}function Ll(e){let t=e.match(/^(-?[0-9.]+)(deg|rad|grad|turn)?$/i);if(!t)return;let[,r,n]=t,o=Number.parseFloat(r);if(!Number.isNaN(o))return o===0?"0":n?`${Y(o)}${n}`:`${Y(o)}deg`}function Ul(e){if(gn.includes(e))return e}function Wl(e){if(e.split(",").every(t=>Sl.includes(t)))return e}function Nl(e){if(["top","left","right","bottom","center"].includes(e))return e}var Bl={__proto__:null,auto:Rl,bracket:Vl,bracketOfColor:Al,bracketOfLength:Ml,bracketOfPosition:_l,cssvar:Pl,degree:Ll,fraction:Ol,global:Ul,number:jl,numberWithUnit:Cl,percent:zl,position:Nl,properties:Wl,px:El,rem:Tl,time:Fl},Dl=ut(Bl),F=Dl;function $s(e,t,r="colors"){let n=e[r],o=-1;for(let i of t){if(o+=1,n&&typeof n!="string"){let s=t.slice(o).join("-").replace(/(-[a-z])/g,a=>a.slice(1).toUpperCase());if(n[s])return n[s];if(n[i]){n=n[i];continue}}return}return n}function Or(e,t,r){return $s(e,t,r)||$s(e,t,"colors")}function Il(e,t){let[r,n]=ae(e,"[","]",["/",":"])??[];if(r!=null){let o=(r.match(Rs)??[])[1];if(o==null||o===t)return[r,n]}}function Kl(e,t,r){let n=Il(e,"color");if(!n)return;let[o,i]=n,s=o.replace(/([a-z])(\d)/g,"$1-$2").split(/-/g),[a]=s;if(!a)return;let c,u=F.bracketOfColor(o),p=u||o;if(F.numberWithUnit(p))return;if(/^#[\da-f]+$/i.test(p)?c=p:/^hex-[\da-fA-F]+$/.test(p)?c=`#${p.slice(4)}`:o.startsWith("$")&&(c=F.cssvar(o)),c=c||u,!c){let d=Or(t,[o],r);typeof d=="string"&&(c=d)}let f="DEFAULT";if(!c){let d,[m]=s.slice(-1);/^\d+$/.test(m)?(f=m,d=Or(t,s.slice(0,-1),r),!d||typeof d=="string"?c=void 0:c=d[f]):(d=Or(t,s,r),!d&&s.length<=2&&([,f=f]=s,d=Or(t,[a],r)),typeof d=="string"?c=d:f&&d&&(c=d[f]))}return{opacity:i,name:a,no:f,color:c,cssColor:I(c),alpha:F.bracket.cssvar.percent(i??"")}}function xn(e,t,r,n){return([,o],{theme:i})=>{let s=Kl(o,i,r);if(!s)return;let{alpha:a,color:c,cssColor:u}=s,p={};if(u)if(a!=null)p[e]=M(u,a);else{let f=`--un-${t}-opacity`,d=M(u,`var(${f})`);d.includes(f)&&(p[f]=ee(u)),p[e]=d}else if(c)if(a!=null)p[e]=M(c,a);else{let f=`--un-${t}-opacity`,d=M(c,`var(${f})`);d.includes(f)&&(p[f]=1),p[e]=d}if(n?.(p)!==!1)return p}}function ue(e,t){return gn.map(r=>[`${e}-${r}`,{[t??e]:r}])}function Ts(e){return e!=null&&$l.test(e)}function Ar(e,t,r){let n=t.split(kl);return e||!e&&n.length===1?vl[e].map(o=>[`--un-${r}${o}`,t]):n.map((o,i)=>[`--un-${r}-${wl[i]}`,o])}var Hl=["auto","default","none","context-menu","help","pointer","progress","wait","cell","crosshair","text","vertical-text","alias","copy","move","no-drop","not-allowed","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out"];var Vs=" ";var r0=[["visible",{visibility:"visible"}],["invisible",{visibility:"hidden"}],["backface-visible",{"backface-visibility":"visible"}],["backface-hidden",{"backface-visibility":"hidden"}],...ue("backface","backface-visibility")],n0=[[/^cursor-(.+)$/,([,e])=>({cursor:F.bracket.cssvar.global(e)})],...Hl.map(e=>[`cursor-${e}`,{cursor:e}])];var o0=[["pointer-events-auto",{"pointer-events":"auto"}],["pointer-events-none",{"pointer-events":"none"}],...ue("pointer-events")],i0=[["resize-x",{resize:"horizontal"}],["resize-y",{resize:"vertical"}],["resize",{resize:"both"}],["resize-none",{resize:"none"}],...ue("resize")],s0=[["select-auto",{"-webkit-user-select":"auto","user-select":"auto"}],["select-all",{"-webkit-user-select":"all","user-select":"all"}],["select-text",{"-webkit-user-select":"text","user-select":"text"}],["select-none",{"-webkit-user-select":"none","user-select":"none"}],...ue("select","user-select")];var a0=[[/^intrinsic-size-(.+)$/,([,e])=>({"contain-intrinsic-size":F.bracket.cssvar.global.fraction.rem(e)}),{autocomplete:"intrinsic-size-<num>"}],["content-visibility-visible",{"content-visibility":"visible"}],["content-visibility-hidden",{"content-visibility":"hidden"}],["content-visibility-auto",{"content-visibility":"auto"}],...ue("content-visibility")];var c0=[["case-upper",{"text-transform":"uppercase"}],["case-lower",{"text-transform":"lowercase"}],["case-capital",{"text-transform":"capitalize"}],["case-normal",{"text-transform":"none"}],...ue("case","text-transform")];var As={"--un-ring-inset":Vs,"--un-ring-offset-width":"0px","--un-ring-offset-color":"#fff","--un-ring-width":"0px","--un-ring-color":"rgb(147 197 253 / 0.5)","--un-shadow":"0 0 rgb(0 0 0 / 0)"},l0=[[/^ring(?:-(.+))?$/,([,e],{theme:t})=>{let r=t.ringWidth?.[e||"DEFAULT"]??F.px(e||"1");if(r)return{"--un-ring-width":r,"--un-ring-offset-shadow":"var(--un-ring-inset) 0 0 0 var(--un-ring-offset-width) var(--un-ring-offset-color)","--un-ring-shadow":"var(--un-ring-inset) 0 0 0 calc(var(--un-ring-width) + var(--un-ring-offset-width)) var(--un-ring-color)","box-shadow":"var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow)"}},{autocomplete:"ring-$ringWidth"}],[/^ring-(?:width-|size-)(.+)$/,Ms,{autocomplete:"ring-(width|size)-$lineWidth"}],["ring-offset",{"--un-ring-offset-width":"1px"}],[/^ring-offset-(?:width-|size-)?(.+)$/,([,e],{theme:t})=>({"--un-ring-offset-width":t.lineWidth?.[e]??F.bracket.cssvar.px(e)}),{autocomplete:"ring-offset-(width|size)-$lineWidth"}],[/^ring-(.+)$/,Gl,{autocomplete:"ring-$colors"}],[/^ring-op(?:acity)?-?(.+)$/,([,e])=>({"--un-ring-opacity":F.bracket.percent.cssvar(e)}),{autocomplete:"ring-(op|opacity)-<percent>"}],[/^ring-offset-(.+)$/,xn("--un-ring-offset-color","ring-offset","borderColor"),{autocomplete:"ring-offset-$colors"}],[/^ring-offset-op(?:acity)?-?(.+)$/,([,e])=>({"--un-ring-offset-opacity":F.bracket.percent.cssvar(e)}),{autocomplete:"ring-offset-(op|opacity)-<percent>"}],["ring-inset",{"--un-ring-inset":"inset"}]];function Ms([,e],{theme:t}){return{"--un-ring-width":t.ringWidth?.[e]??F.bracket.cssvar.px(e)}}function Gl(e,t){return Ts(F.bracket(e[1]))?Ms(e,t):xn("--un-ring-color","ring","borderColor")(e,t)}var _s={"--un-ring-offset-shadow":"0 0 rgb(0 0 0 / 0)","--un-ring-shadow":"0 0 rgb(0 0 0 / 0)","--un-shadow-inset":Vs,"--un-shadow":"0 0 rgb(0 0 0 / 0)"};var Mr=["translate","rotate","scale"],ql=["translateX(var(--un-translate-x))","translateY(var(--un-translate-y))","rotate(var(--un-rotate))","rotateZ(var(--un-rotate-z))","skewX(var(--un-skew-x))","skewY(var(--un-skew-y))","scaleX(var(--un-scale-x))","scaleY(var(--un-scale-y))"].join(" "),ze=["translateX(var(--un-translate-x))","translateY(var(--un-translate-y))","translateZ(var(--un-translate-z))","rotate(var(--un-rotate))","rotateX(var(--un-rotate-x))","rotateY(var(--un-rotate-y))","rotateZ(var(--un-rotate-z))","skewX(var(--un-skew-x))","skewY(var(--un-skew-y))","scaleX(var(--un-scale-x))","scaleY(var(--un-scale-y))","scaleZ(var(--un-scale-z))"].join(" "),Yl=["translate3d(var(--un-translate-x), var(--un-translate-y), var(--un-translate-z))","rotate(var(--un-rotate))","rotateX(var(--un-rotate-x))","rotateY(var(--un-rotate-y))","rotateZ(var(--un-rotate-z))","skewX(var(--un-skew-x))","skewY(var(--un-skew-y))","scaleX(var(--un-scale-x))","scaleY(var(--un-scale-y))","scaleZ(var(--un-scale-z))"].join(" "),Ps={"--un-rotate":0,"--un-rotate-x":0,"--un-rotate-y":0,"--un-rotate-z":0,"--un-scale-x":1,"--un-scale-y":1,"--un-scale-z":1,"--un-skew-x":0,"--un-skew-y":0,"--un-translate-x":0,"--un-translate-y":0,"--un-translate-z":0},u0=[[/^(?:transform-)?origin-(.+)$/,([,e])=>({"transform-origin":Xe[e]??F.bracket.cssvar(e)}),{autocomplete:[`transform-origin-(${Object.keys(Xe).join("|")})`,`origin-(${Object.keys(Xe).join("|")})`]}],[/^(?:transform-)?perspect(?:ive)?-(.+)$/,([,e])=>{let t=F.bracket.cssvar.px.numberWithUnit(e);if(t!=null)return{"-webkit-perspective":t,perspective:t}}],[/^(?:transform-)?perspect(?:ive)?-origin-(.+)$/,([,e])=>{let t=F.bracket.cssvar(e)??(e.length>=3?Xe[e]:void 0);if(t!=null)return{"-webkit-perspective-origin":t,"perspective-origin":t}}],[/^(?:transform-)?translate-()(.+)$/,Es],[/^(?:transform-)?translate-([xyz])-(.+)$/,Es],[/^(?:transform-)?rotate-()(.+)$/,zs],[/^(?:transform-)?rotate-([xyz])-(.+)$/,zs],[/^(?:transform-)?skew-()(.+)$/,Os],[/^(?:transform-)?skew-([xy])-(.+)$/,Os,{autocomplete:["transform-skew-(x|y)-<percent>","skew-(x|y)-<percent>"]}],[/^(?:transform-)?scale-()(.+)$/,js],[/^(?:transform-)?scale-([xyz])-(.+)$/,js,{autocomplete:[`transform-(${Mr.join("|")})-<percent>`,`transform-(${Mr.join("|")})-(x|y|z)-<percent>`,`(${Mr.join("|")})-<percent>`,`(${Mr.join("|")})-(x|y|z)-<percent>`]}],[/^(?:transform-)?preserve-3d$/,()=>({"transform-style":"preserve-3d"})],[/^(?:transform-)?preserve-flat$/,()=>({"transform-style":"flat"})],["transform",{transform:ze}],["transform-cpu",{transform:ql}],["transform-gpu",{transform:Yl}],["transform-none",{transform:"none"}],...ue("transform")];function Es([,e,t],{theme:r}){let n=r.spacing?.[t]??F.bracket.cssvar.fraction.rem(t);if(n!=null)return[...Ar(e,n,"translate"),["transform",ze]]}function js([,e,t]){let r=F.bracket.cssvar.fraction.percent(t);if(r!=null)return[...Ar(e,r,"scale"),["transform",ze]]}function zs([,e="",t]){let r=F.bracket.cssvar.degree(t);if(r!=null)return e?{"--un-rotate":0,[`--un-rotate-${e}`]:r,transform:ze}:{"--un-rotate-x":0,"--un-rotate-y":0,"--un-rotate-z":0,"--un-rotate":r,transform:ze}}function Os([,e,t]){let r=F.bracket.cssvar.degree(t);if(r!=null)return[...Ar(e,r,"skew"),["transform",ze]]}var Fs={sans:["ui-sans-serif","system-ui","-apple-system","BlinkMacSystemFont",'"Segoe UI"',"Roboto",'"Helvetica Neue"',"Arial",'"Noto Sans"',"sans-serif",'"Apple Color Emoji"','"Segoe UI Emoji"','"Segoe UI Symbol"','"Noto Color Emoji"'].join(","),serif:["ui-serif","Georgia","Cambria",'"Times New Roman"',"Times","serif"].join(","),mono:["ui-monospace","SFMono-Regular","Menlo","Monaco","Consolas",'"Liberation Mono"','"Courier New"',"monospace"].join(",")},Ls={xs:["0.75rem","1rem"],sm:["0.875rem","1.25rem"],base:["1rem","1.5rem"],lg:["1.125rem","1.75rem"],xl:["1.25rem","1.75rem"],"2xl":["1.5rem","2rem"],"3xl":["1.875rem","2.25rem"],"4xl":["2.25rem","2.5rem"],"5xl":["3rem","1"],"6xl":["3.75rem","1"],"7xl":["4.5rem","1"],"8xl":["6rem","1"],"9xl":["8rem","1"]},Us={DEFAULT:"1.5rem",xs:"0.5rem",sm:"1rem",md:"1.5rem",lg:"2rem",xl:"2.5rem","2xl":"3rem","3xl":"4rem"},Ws={DEFAULT:"1.5rem",none:"0",sm:"thin",md:"medium",lg:"thick"},Ns={DEFAULT:["0 0 1px rgb(0 0 0 / 0.2)","0 0 1px rgb(1 0 5 / 0.1)"],none:"0 0 rgb(0 0 0 / 0)",sm:"1px 1px 3px rgb(36 37 47 / 0.25)",md:["0 1px 2px rgb(30 29 39 / 0.19)","1px 2px 4px rgb(54 64 147 / 0.18)"],lg:["3px 3px 6px rgb(0 0 0 / 0.26)","0 0 5px rgb(15 3 86 / 0.22)"],xl:["1px 1px 3px rgb(0 0 0 / 0.29)","2px 4px 7px rgb(73 64 125 / 0.35)"]},Bs={none:"1",tight:"1.25",snug:"1.375",normal:"1.5",relaxed:"1.625",loose:"2"},wn={tighter:"-0.05em",tight:"-0.025em",normal:"0em",wide:"0.025em",wider:"0.05em",widest:"0.1em"},Ds={thin:"100",extralight:"200",light:"300",normal:"400",medium:"500",semibold:"600",bold:"700",extrabold:"800",black:"900"},Is=wn,$n={sm:"640px",md:"768px",lg:"1024px",xl:"1280px","2xl":"1536px"},Ks={...$n},Hs={DEFAULT:"1px",none:"0"},Gs={DEFAULT:"1rem",none:"0",xs:"0.75rem",sm:"0.875rem",lg:"1.125rem",xl:"1.25rem","2xl":"1.5rem","3xl":"1.875rem","4xl":"2.25rem","5xl":"3rem","6xl":"3.75rem","7xl":"4.5rem","8xl":"6rem","9xl":"8rem"},qs={DEFAULT:"150ms",none:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms"},Ys={DEFAULT:"0.25rem",none:"0",sm:"0.125rem",md:"0.375rem",lg:"0.5rem",xl:"0.75rem","2xl":"1rem","3xl":"1.5rem",full:"9999px"},Xs={DEFAULT:["var(--un-shadow-inset) 0 1px 3px 0 rgb(0 0 0 / 0.1)","var(--un-shadow-inset) 0 1px 2px -1px rgb(0 0 0 / 0.1)"],none:"0 0 rgb(0 0 0 / 0)",sm:"var(--un-shadow-inset) 0 1px 2px 0 rgb(0 0 0 / 0.05)",md:["var(--un-shadow-inset) 0 4px 6px -1px rgb(0 0 0 / 0.1)","var(--un-shadow-inset) 0 2px 4px -2px rgb(0 0 0 / 0.1)"],lg:["var(--un-shadow-inset) 0 10px 15px -3px rgb(0 0 0 / 0.1)","var(--un-shadow-inset) 0 4px 6px -4px rgb(0 0 0 / 0.1)"],xl:["var(--un-shadow-inset) 0 20px 25px -5px rgb(0 0 0 / 0.1)","var(--un-shadow-inset) 0 8px 10px -6px rgb(0 0 0 / 0.1)"],"2xl":"var(--un-shadow-inset) 0 25px 50px -12px rgb(0 0 0 / 0.25)",inner:"inset 0 2px 4px 0 rgb(0 0 0 / 0.05)"},Zs={DEFAULT:"cubic-bezier(0.4, 0, 0.2, 1)",linear:"linear",in:"cubic-bezier(0.4, 0, 1, 1)",out:"cubic-bezier(0, 0, 0.2, 1)","in-out":"cubic-bezier(0.4, 0, 0.2, 1)"},Js={DEFAULT:"3px",none:"0"},Qs={auto:"auto"},ea={mouse:"(hover) and (pointer: fine)"},ta={DEFAULT:"8px",0:"0",sm:"4px",md:"12px",lg:"16px",xl:"24px","2xl":"40px","3xl":"64px"},ra={DEFAULT:["0 1px 2px rgb(0 0 0 / 0.1)","0 1px 1px rgb(0 0 0 / 0.06)"],sm:"0 1px 1px rgb(0 0 0 / 0.05)",md:["0 4px 3px rgb(0 0 0 / 0.07)","0 2px 2px rgb(0 0 0 / 0.06)"],lg:["0 10px 8px rgb(0 0 0 / 0.04)","0 4px 3px rgb(0 0 0 / 0.1)"],xl:["0 20px 13px rgb(0 0 0 / 0.03)","0 8px 5px rgb(0 0 0 / 0.08)"],"2xl":"0 25px 25px rgb(0 0 0 / 0.15)",none:"0 0 rgb(0 0 0 / 0)"},Oe={xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",prose:"65ch"},yn={auto:"auto",...Oe,screen:"100vw"},Ze={none:"none",...Oe,screen:"100vw"},vn={auto:"auto",...Oe,screen:"100vh"},Je={none:"none",...Oe,screen:"100vh"},na=Object.fromEntries(Object.entries(Oe).map(([e,t])=>[e,`(min-width: ${t})`])),oa={...Ps,..._s,...As},kn={width:yn,height:vn,maxWidth:Ze,maxHeight:Je,minWidth:Ze,minHeight:Je,inlineSize:yn,blockSize:vn,maxInlineSize:Ze,maxBlockSize:Je,minInlineSize:Ze,minBlockSize:Je,colors:hn,fontFamily:Fs,fontSize:Ls,fontWeight:Ds,breakpoints:$n,verticalBreakpoints:Ks,borderRadius:Ys,lineHeight:Bs,letterSpacing:wn,wordSpacing:Is,boxShadow:Xs,textIndent:Us,textShadow:Ns,textStrokeWidth:Ws,blur:ta,dropShadow:ra,easing:Zs,lineWidth:Hs,spacing:Gs,duration:qs,ringWidth:Js,preflightBase:oa,containers:na,zIndex:Qs,media:ea};var ia={...kn,aria:{busy:'busy="true"',checked:'checked="true"',disabled:'disabled="true"',expanded:'expanded="true"',hidden:'hidden="true"',pressed:'pressed="true"',readonly:'readonly="true"',required:'required="true"',selected:'selected="true"'},animation:{keyframes:{pulse:"{0%, 100% {opacity:1} 50% {opacity:.5}}",bounce:"{0%, 100% {transform:translateY(-25%);animation-timing-function:cubic-bezier(0.8,0,1,1)} 50% {transform:translateY(0);animation-timing-function:cubic-bezier(0,0,0.2,1)}}",spin:"{from{transform:rotate(0deg)}to{transform:rotate(360deg)}}",ping:"{0%{transform:scale(1);opacity:1}75%,100%{transform:scale(2);opacity:0}}","bounce-alt":"{from,20%,53%,80%,to{animation-timing-function:cubic-bezier(0.215,0.61,0.355,1);transform:translate3d(0,0,0)}40%,43%{animation-timing-function:cubic-bezier(0.755,0.05,0.855,0.06);transform:translate3d(0,-30px,0)}70%{animation-timing-function:cubic-bezier(0.755,0.05,0.855,0.06);transform:translate3d(0,-15px,0)}90%{transform:translate3d(0,-4px,0)}}",flash:"{from,50%,to{opacity:1}25%,75%{opacity:0}}","pulse-alt":"{from{transform:scale3d(1,1,1)}50%{transform:scale3d(1.05,1.05,1.05)}to{transform:scale3d(1,1,1)}}","rubber-band":"{from{transform:scale3d(1,1,1)}30%{transform:scale3d(1.25,0.75,1)}40%{transform:scale3d(0.75,1.25,1)}50%{transform:scale3d(1.15,0.85,1)}65%{transform:scale3d(0.95,1.05,1)}75%{transform:scale3d(1.05,0.95,1)}to{transform:scale3d(1,1,1)}}","shake-x":"{from,to{transform:translate3d(0,0,0)}10%,30%,50%,70%,90%{transform:translate3d(-10px,0,0)}20%,40%,60%,80%{transform:translate3d(10px,0,0)}}","shake-y":"{from,to{transform:translate3d(0,0,0)}10%,30%,50%,70%,90%{transform:translate3d(0,-10px,0)}20%,40%,60%,80%{transform:translate3d(0,10px,0)}}","head-shake":"{0%{transform:translateX(0)}6.5%{transform:translateX(-6px) rotateY(-9deg)}18.5%{transform:translateX(5px) rotateY(7deg)}31.5%{transform:translateX(-3px) rotateY(-5deg)}43.5%{transform:translateX(2px) rotateY(3deg)}50%{transform:translateX(0)}}",swing:"{20%{transform:rotate3d(0,0,1,15deg)}40%{transform:rotate3d(0,0,1,-10deg)}60%{transform:rotate3d(0,0,1,5deg)}80%{transform:rotate3d(0,0,1,-5deg)}to{transform:rotate3d(0,0,1,0deg)}}",tada:"{from{transform:scale3d(1,1,1)}10%,20%{transform:scale3d(0.9,0.9,0.9) rotate3d(0,0,1,-3deg)}30%,50%,70%,90%{transform:scale3d(1.1,1.1,1.1) rotate3d(0,0,1,3deg)}40%,60%,80%{transform:scale3d(1.1,1.1,1.1) rotate3d(0,0,1,-3deg)}to{transform:scale3d(1,1,1)}}",wobble:"{from{transform:translate3d(0,0,0)}15%{transform:translate3d(-25%,0,0) rotate3d(0,0,1,-5deg)}30%{transform:translate3d(20%,0,0) rotate3d(0,0,1,3deg)}45%{transform:translate3d(-15%,0,0) rotate3d(0,0,1,-3deg)}60%{transform:translate3d(10%,0,0) rotate3d(0,0,1,2deg)}75%{transform:translate3d(-5%,0,0) rotate3d(0,0,1,-1deg)}to{transform:translate3d(0,0,0)}}",jello:"{from,11.1%,to{transform:translate3d(0,0,0)}22.2%{transform:skewX(-12.5deg) skewY(-12.5deg)}33.3%{transform:skewX(6.25deg) skewY(6.25deg)}44.4%{transform:skewX(-3.125deg)skewY(-3.125deg)}55.5%{transform:skewX(1.5625deg) skewY(1.5625deg)}66.6%{transform:skewX(-0.78125deg) skewY(-0.78125deg)}77.7%{transform:skewX(0.390625deg) skewY(0.390625deg)}88.8%{transform:skewX(-0.1953125deg) skewY(-0.1953125deg)}}","heart-beat":"{0%{transform:scale(1)}14%{transform:scale(1.3)}28%{transform:scale(1)}42%{transform:scale(1.3)}70%{transform:scale(1)}}",hinge:"{0%{transform-origin:top left;animation-timing-function:ease-in-out}20%,60%{transform:rotate3d(0,0,1,80deg);transform-origin:top left;animation-timing-function:ease-in-out}40%,80%{transform:rotate3d(0,0,1,60deg);transform-origin:top left;animation-timing-function:ease-in-out}to{transform:translate3d(0,700px,0);opacity:0}}","jack-in-the-box":"{from{opacity:0;transform-origin:center bottom;transform:scale(0.1) rotate(30deg)}50%{transform:rotate(-10deg)}70%{transform:rotate(3deg)}to{transform:scale(1)}}","light-speed-in-left":"{from{opacity:0;transform:translate3d(-100%,0,0) skewX(-30deg)}60%{opacity:1;transform:skewX(20deg)}80%{transform:skewX(-5deg)}to{transform:translate3d(0,0,0)}}","light-speed-in-right":"{from{opacity:0;transform:translate3d(100%,0,0) skewX(-30deg)}60%{opacity:1;transform:skewX(20deg)}80%{transform:skewX(-5deg)}to{transform:translate3d(0,0,0)}}","light-speed-out-left":"{from{opacity:1}to{opacity:0;transform:translate3d(-100%,0,0) skewX(30deg)}}","light-speed-out-right":"{from{opacity:1}to{opacity:0;transform:translate3d(100%,0,0) skewX(30deg)}}",flip:"{from{transform:perspective(400px) scale3d(1,1,1) translate3d(0,0,0) rotate3d(0,1,0,-360deg);animation-timing-function:ease-out}40%{transform:perspective(400px) scale3d(1,1,1) translate3d(0,0,150px) rotate3d(0,1,0,-190deg);animation-timing-function:ease-out}50%{transform:perspective(400px) scale3d(1,1,1) translate3d(0,0,150px) rotate3d(0,1,0,-170deg);animation-timing-function:ease-in}80%{transform:perspective(400px) scale3d(0.95,0.95,0.95) translate3d(0,0,0) rotate3d(0,1,0,0deg);animation-timing-function:ease-in}to{transform:perspective(400px) scale3d(1,1,1) translate3d(0,0,0) rotate3d(0,1,0,0deg);animation-timing-function:ease-in}}","flip-in-x":"{from{transform:perspective(400px) rotate3d(1,0,0,90deg);animation-timing-function:ease-in;opacity:0}40%{transform:perspective(400px) rotate3d(1,0,0,-20deg);animation-timing-function:ease-in}60%{transform:perspective(400px) rotate3d(1,0,0,10deg);opacity:1}80%{transform:perspective(400px) rotate3d(1,0,0,-5deg)}to{transform:perspective(400px)}}","flip-in-y":"{from{transform:perspective(400px) rotate3d(0,1,0,90deg);animation-timing-function:ease-in;opacity:0}40%{transform:perspective(400px) rotate3d(0,1,0,-20deg);animation-timing-function:ease-in}60%{transform:perspective(400px) rotate3d(0,1,0,10deg);opacity:1}80%{transform:perspective(400px) rotate3d(0,1,0,-5deg)}to{transform:perspective(400px)}}","flip-out-x":"{from{transform:perspective(400px)}30%{transform:perspective(400px) rotate3d(1,0,0,-20deg);opacity:1}to{transform:perspective(400px) rotate3d(1,0,0,90deg);opacity:0}}","flip-out-y":"{from{transform:perspective(400px)}30%{transform:perspective(400px) rotate3d(0,1,0,-15deg);opacity:1}to{transform:perspective(400px) rotate3d(0,1,0,90deg);opacity:0}}","rotate-in":"{from{transform-origin:center;transform:rotate3d(0,0,1,-200deg);opacity:0}to{transform-origin:center;transform:translate3d(0,0,0);opacity:1}}","rotate-in-down-left":"{from{transform-origin:left bottom;transform:rotate3d(0,0,1,-45deg);opacity:0}to{transform-origin:left bottom;transform:translate3d(0,0,0);opacity:1}}","rotate-in-down-right":"{from{transform-origin:right bottom;transform:rotate3d(0,0,1,45deg);opacity:0}to{transform-origin:right bottom;transform:translate3d(0,0,0);opacity:1}}","rotate-in-up-left":"{from{transform-origin:left top;transform:rotate3d(0,0,1,45deg);opacity:0}to{transform-origin:left top;transform:translate3d(0,0,0);opacity:1}}","rotate-in-up-right":"{from{transform-origin:right bottom;transform:rotate3d(0,0,1,-90deg);opacity:0}to{transform-origin:right bottom;transform:translate3d(0,0,0);opacity:1}}","rotate-out":"{from{transform-origin:center;opacity:1}to{transform-origin:center;transform:rotate3d(0,0,1,200deg);opacity:0}}","rotate-out-down-left":"{from{transform-origin:left bottom;opacity:1}to{transform-origin:left bottom;transform:rotate3d(0,0,1,45deg);opacity:0}}","rotate-out-down-right":"{from{transform-origin:right bottom;opacity:1}to{transform-origin:right bottom;transform:rotate3d(0,0,1,-45deg);opacity:0}}","rotate-out-up-left":"{from{transform-origin:left bottom;opacity:1}to{transform-origin:left bottom;transform:rotate3d(0,0,1,-45deg);opacity:0}}","rotate-out-up-right":"{from{transform-origin:right bottom;opacity:1}to{transform-origin:left bottom;transform:rotate3d(0,0,1,90deg);opacity:0}}","roll-in":"{from{opacity:0;transform:translate3d(-100%,0,0) rotate3d(0,0,1,-120deg)}to{opacity:1;transform:translate3d(0,0,0)}}","roll-out":"{from{opacity:1}to{opacity:0;transform:translate3d(100%,0,0) rotate3d(0,0,1,120deg)}}","zoom-in":"{from{opacity:0;transform:scale3d(0.3,0.3,0.3)}50%{opacity:1}}","zoom-in-down":"{from{opacity:0;transform:scale3d(0.1,0.1,0.1) translate3d(0,-1000px,0);animation-timing-function:cubic-bezier(0.55,0.055,0.675,0.19)}60%{opacity:1;transform:scale3d(0.475,0.475,0.475) translate3d(0,60px,0);animation-timing-function:cubic-bezier(0.175,0.885,0.32,1)}}","zoom-in-left":"{from{opacity:0;transform:scale3d(0.1,0.1,0.1) translate3d(-1000px,0,0);animation-timing-function:cubic-bezier(0.55,0.055,0.675,0.19)}60%{opacity:1;transform:scale3d(0.475,0.475,0.475) translate3d(10px,0,0);animation-timing-function:cubic-bezier(0.175,0.885,0.32,1)}}","zoom-in-right":"{from{opacity:0;transform:scale3d(0.1,0.1,0.1) translate3d(1000px,0,0);animation-timing-function:cubic-bezier(0.55,0.055,0.675,0.19)}60%{opacity:1;transform:scale3d(0.475,0.475,0.475) translate3d(-10px,0,0);animation-timing-function:cubic-bezier(0.175,0.885,0.32,1)}}","zoom-in-up":"{from{opacity:0;transform:scale3d(0.1,0.1,0.1) translate3d(0,1000px,0);animation-timing-function:cubic-bezier(0.55,0.055,0.675,0.19)}60%{opacity:1;transform:scale3d(0.475,0.475,0.475) translate3d(0,-60px,0);animation-timing-function:cubic-bezier(0.175,0.885,0.32,1)}}","zoom-out":"{from{opacity:1}50%{opacity:0;transform:scale3d(0.3,0.3,0.3)}to{opacity:0}}","zoom-out-down":"{40%{opacity:1;transform:scale3d(0.475,0.475,0.475) translate3d(0,-60px,0);animation-timing-function:cubic-bezier(0.55,0.055,0.675,0.19)}to{opacity:0;transform:scale3d(0.1,0.1,0.1) translate3d(0,2000px,0);transform-origin:center bottom;animation-timing-function:cubic-bezier(0.175,0.885,0.32,1)}}","zoom-out-left":"{40%{opacity:1;transform:scale3d(0.475,0.475,0.475) translate3d(42px,0,0)}to{opacity:0;transform:scale(0.1) translate3d(-2000px,0,0);transform-origin:left center}}","zoom-out-right":"{40%{opacity:1;transform:scale3d(0.475,0.475,0.475) translate3d(-42px,0,0)}to{opacity:0;transform:scale(0.1) translate3d(2000px,0,0);transform-origin:right center}}","zoom-out-up":"{40%{opacity:1;transform:scale3d(0.475,0.475,0.475) translate3d(0,60px,0);animation-timing-function:cubic-bezier(0.55,0.055,0.675,0.19)}to{opacity:0;transform:scale3d(0.1,0.1,0.1) translate3d(0,-2000px,0);transform-origin:center bottom;animation-timing-function:cubic-bezier(0.175,0.885,0.32,1)}}","bounce-in":"{from,20%,40%,60%,80%,to{animation-timing-function:ease-in-out}0%{opacity:0;transform:scale3d(0.3,0.3,0.3)}20%{transform:scale3d(1.1,1.1,1.1)}40%{transform:scale3d(0.9,0.9,0.9)}60%{transform:scale3d(1.03,1.03,1.03);opacity:1}80%{transform:scale3d(0.97,0.97,0.97)}to{opacity:1;transform:scale3d(1,1,1)}}","bounce-in-down":"{from,60%,75%,90%,to{animation-timing-function:cubic-bezier(0.215,0.61,0.355,1)}0%{opacity:0;transform:translate3d(0,-3000px,0)}60%{opacity:1;transform:translate3d(0,25px,0)}75%{transform:translate3d(0,-10px,0)}90%{transform:translate3d(0,5px,0)}to{transform:translate3d(0,0,0)}}","bounce-in-left":"{from,60%,75%,90%,to{animation-timing-function:cubic-bezier(0.215,0.61,0.355,1)}0%{opacity:0;transform:translate3d(-3000px,0,0)}60%{opacity:1;transform:translate3d(25px,0,0)}75%{transform:translate3d(-10px,0,0)}90%{transform:translate3d(5px,0,0)}to{transform:translate3d(0,0,0)}}","bounce-in-right":"{from,60%,75%,90%,to{animation-timing-function:cubic-bezier(0.215,0.61,0.355,1)}0%{opacity:0;transform:translate3d(3000px,0,0)}60%{opacity:1;transform:translate3d(-25px,0,0)}75%{transform:translate3d(10px,0,0)}90%{transform:translate3d(-5px,0,0)}to{transform:translate3d(0,0,0)}}","bounce-in-up":"{from,60%,75%,90%,to{animation-timing-function:cubic-bezier(0.215,0.61,0.355,1)}0%{opacity:0;transform:translate3d(0,3000px,0)}60%{opacity:1;transform:translate3d(0,-20px,0)}75%{transform:translate3d(0,10px,0)}90%{transform:translate3d(0,-5px,0)}to{transform:translate3d(0,0,0)}}","bounce-out":"{20%{transform:scale3d(0.9,0.9,0.9)}50%,55%{opacity:1;transform:scale3d(1.1,1.1,1.1)}to{opacity:0;transform:scale3d(0.3,0.3,0.3)}}","bounce-out-down":"{20%{transform:translate3d(0,10px,0)}40%,45%{opacity:1;transform:translate3d(0,-20px,0)}to{opacity:0;transform:translate3d(0,2000px,0)}}","bounce-out-left":"{20%{opacity:1;transform:translate3d(20px,0,0)}to{opacity:0;transform:translate3d(-2000px,0,0)}}","bounce-out-right":"{20%{opacity:1;transform:translate3d(-20px,0,0)}to{opacity:0;transform:translate3d(2000px,0,0)}}","bounce-out-up":"{20%{transform:translate3d(0,-10px,0)}40%,45%{opacity:1;transform:translate3d(0,20px,0)}to{opacity:0;transform:translate3d(0,-2000px,0)}}","slide-in-down":"{from{transform:translate3d(0,-100%,0);visibility:visible}to{transform:translate3d(0,0,0)}}","slide-in-left":"{from{transform:translate3d(-100%,0,0);visibility:visible}to{transform:translate3d(0,0,0)}}","slide-in-right":"{from{transform:translate3d(100%,0,0);visibility:visible}to{transform:translate3d(0,0,0)}}","slide-in-up":"{from{transform:translate3d(0,100%,0);visibility:visible}to{transform:translate3d(0,0,0)}}","slide-out-down":"{from{transform:translate3d(0,0,0)}to{visibility:hidden;transform:translate3d(0,100%,0)}}","slide-out-left":"{from{transform:translate3d(0,0,0)}to{visibility:hidden;transform:translate3d(-100%,0,0)}}","slide-out-right":"{from{transform:translate3d(0,0,0)}to{visibility:hidden;transform:translate3d(100%,0,0)}}","slide-out-up":"{from{transform:translate3d(0,0,0)}to{visibility:hidden;transform:translate3d(0,-100%,0)}}","fade-in":"{from{opacity:0}to{opacity:1}}","fade-in-down":"{from{opacity:0;transform:translate3d(0,-100%,0)}to{opacity:1;transform:translate3d(0,0,0)}}","fade-in-down-big":"{from{opacity:0;transform:translate3d(0,-2000px,0)}to{opacity:1;transform:translate3d(0,0,0)}}","fade-in-left":"{from{opacity:0;transform:translate3d(-100%,0,0)}to{opacity:1;transform:translate3d(0,0,0)}}","fade-in-left-big":"{from{opacity:0;transform:translate3d(-2000px,0,0)}to{opacity:1;transform:translate3d(0,0,0)}}","fade-in-right":"{from{opacity:0;transform:translate3d(100%,0,0)}to{opacity:1;transform:translate3d(0,0,0)}}","fade-in-right-big":"{from{opacity:0;transform:translate3d(2000px,0,0)}to{opacity:1;transform:translate3d(0,0,0)}}","fade-in-up":"{from{opacity:0;transform:translate3d(0,100%,0)}to{opacity:1;transform:translate3d(0,0,0)}}","fade-in-up-big":"{from{opacity:0;transform:translate3d(0,2000px,0)}to{opacity:1;transform:translate3d(0,0,0)}}","fade-in-top-left":"{from{opacity:0;transform:translate3d(-100%,-100%,0)}to{opacity:1;transform:translate3d(0,0,0)}}","fade-in-top-right":"{from{opacity:0;transform:translate3d(100%,-100%,0)}to{opacity:1;transform:translate3d(0,0,0)}}","fade-in-bottom-left":"{from{opacity:0;transform:translate3d(-100%,100%,0)}to{opacity:1;transform:translate3d(0,0,0)}}","fade-in-bottom-right":"{from{opacity:0;transform:translate3d(100%,100%,0)}to{opacity:1;transform:translate3d(0,0,0)}}","fade-out":"{from{opacity:1}to{opacity:0}}","fade-out-down":"{from{opacity:1}to{opacity:0;transform:translate3d(0,100%,0)}}","fade-out-down-big":"{from{opacity:1}to{opacity:0;transform:translate3d(0,2000px,0)}}","fade-out-left":"{from{opacity:1}to{opacity:0;transform:translate3d(-100%,0,0)}}","fade-out-left-big":"{from{opacity:1}to{opacity:0;transform:translate3d(-2000px,0,0)}}","fade-out-right":"{from{opacity:1}to{opacity:0;transform:translate3d(100%,0,0)}}","fade-out-right-big":"{from{opacity:1}to{opacity:0;transform:translate3d(2000px,0,0)}}","fade-out-up":"{from{opacity:1}to{opacity:0;transform:translate3d(0,-100%,0)}}","fade-out-up-big":"{from{opacity:1}to{opacity:0;transform:translate3d(0,-2000px,0)}}","fade-out-top-left":"{from{opacity:1;transform:translate3d(0,0,0)}to{opacity:0;transform:translate3d(-100%,-100%,0)}}","fade-out-top-right":"{from{opacity:1;transform:translate3d(0,0,0)}to{opacity:0;transform:translate3d(100%,-100%,0)}}","fade-out-bottom-left":"{from{opacity:1;transform:translate3d(0,0,0)}to{opacity:0;transform:translate3d(-100%,100%,0)}}","fade-out-bottom-right":"{from{opacity:1;transform:translate3d(0,0,0)}to{opacity:0;transform:translate3d(100%,100%,0)}}","back-in-up":"{0%{opacity:0.7;transform:translateY(1200px) scale(0.7)}80%{opacity:0.7;transform:translateY(0px) scale(0.7)}100%{opacity:1;transform:scale(1)}}","back-in-down":"{0%{opacity:0.7;transform:translateY(-1200px) scale(0.7)}80%{opacity:0.7;transform:translateY(0px) scale(0.7)}100%{opacity:1;transform:scale(1)}}","back-in-right":"{0%{opacity:0.7;transform:translateX(2000px) scale(0.7)}80%{opacity:0.7;transform:translateY(0px) scale(0.7)}100%{opacity:1;transform:scale(1)}}","back-in-left":"{0%{opacity:0.7;transform:translateX(-2000px) scale(0.7)}80%{opacity:0.7;transform:translateX(0px) scale(0.7)}100%{opacity:1;transform:scale(1)}}","back-out-up":"{0%{opacity:1;transform:scale(1)}80%{opacity:0.7;transform:translateY(0px) scale(0.7)}100%{opacity:0.7;transform:translateY(-700px) scale(0.7)}}","back-out-down":"{0%{opacity:1;transform:scale(1)}80%{opacity:0.7;transform:translateY(0px) scale(0.7)}100%{opacity:0.7;transform:translateY(700px) scale(0.7)}}","back-out-right":"{0%{opacity:1;transform:scale(1)}80%{opacity:0.7;transform:translateY(0px) scale(0.7)}100%{opacity:0.7;transform:translateX(2000px) scale(0.7)}}","back-out-left":"{0%{opacity:1;transform:scale(1)}80%{opacity:0.7;transform:translateX(-2000px) scale(0.7)}100%{opacity:0.7;transform:translateY(-700px) scale(0.7)}}"},durations:{pulse:"2s","heart-beat":"1.3s","bounce-in":"0.75s","bounce-out":"0.75s","flip-out-x":"0.75s","flip-out-y":"0.75s",hinge:"2s"},timingFns:{pulse:"cubic-bezier(0.4,0,.6,1)",ping:"cubic-bezier(0,0,.2,1)","head-shake":"ease-in-out","heart-beat":"ease-in-out","pulse-alt":"ease-in-out","light-speed-in-left":"ease-out","light-speed-in-right":"ease-out","light-speed-out-left":"ease-in","light-speed-out-right":"ease-in"},properties:{"bounce-alt":{"transform-origin":"center bottom"},jello:{"transform-origin":"center"},swing:{"transform-origin":"top center"},flip:{"backface-visibility":"visible"},"flip-in-x":{"backface-visibility":"visible !important"},"flip-in-y":{"backface-visibility":"visible !important"},"flip-out-x":{"backface-visibility":"visible !important"},"flip-out-y":{"backface-visibility":"visible !important"},"rotate-in":{"transform-origin":"center"},"rotate-in-down-left":{"transform-origin":"left bottom"},"rotate-in-down-right":{"transform-origin":"right bottom"},"rotate-in-up-left":{"transform-origin":"left bottom"},"rotate-in-up-right":{"transform-origin":"right bottom"},"rotate-out":{"transform-origin":"center"},"rotate-out-down-left":{"transform-origin":"left bottom"},"rotate-out-down-right":{"transform-origin":"right bottom"},"rotate-out-up-left":{"transform-origin":"left bottom"},"rotate-out-up-right":{"transform-origin":"right bottom"},hinge:{"transform-origin":"top left"},"zoom-out-down":{"transform-origin":"center bottom"},"zoom-out-left":{"transform-origin":"left center"},"zoom-out-right":{"transform-origin":"right center"},"zoom-out-up":{"transform-origin":"center bottom"}},counts:{spin:"infinite",ping:"infinite",pulse:"infinite","pulse-alt":"infinite",bounce:"infinite","bounce-alt":"infinite"}},media:{portrait:"(orientation: portrait)",landscape:"(orientation: landscape)",os_dark:"(prefers-color-scheme: dark)",os_light:"(prefers-color-scheme: light)",motion_ok:"(prefers-reduced-motion: no-preference)",motion_not_ok:"(prefers-reduced-motion: reduce)",high_contrast:"(prefers-contrast: high)",low_contrast:"(prefers-contrast: low)",opacity_ok:"(prefers-reduced-transparency: no-preference)",opacity_not_ok:"(prefers-reduced-transparency: reduce)",use_data_ok:"(prefers-reduced-data: no-preference)",use_data_not_ok:"(prefers-reduced-data: reduce)",touch:"(hover: none) and (pointer: coarse)",stylus:"(hover: none) and (pointer: fine)",pointer:"(hover) and (pointer: coarse)",mouse:"(hover) and (pointer: fine)",hd_color:"(dynamic-range: high)"},supports:{grid:"(display: grid)"},preflightBase:{...yr,...ds,...hs,...fs,...ss,...fr,...lr,...Ii,...Ki}};var sa=[N("svg",e=>({selector:`${e.selector} svg`}))];var aa=[N(".dark",e=>({prefix:`.dark $$ ${e.prefix}`})),N(".light",e=>({prefix:`.light $$ ${e.prefix}`})),B("@dark","@media (prefers-color-scheme: dark)"),B("@light","@media (prefers-color-scheme: light)")];var ca=[B("contrast-more","@media (prefers-contrast: more)"),B("contrast-less","@media (prefers-contrast: less)")],la=[B("motion-reduce","@media (prefers-reduced-motion: reduce)"),B("motion-safe","@media (prefers-reduced-motion: no-preference)")],ua=[B("landscape","@media (orientation: landscape)"),B("portrait","@media (orientation: portrait)")];var fa=e=>{if(!e.startsWith("_")&&(/space-[xy]-.+$/.test(e)||/divide-/.test(e)))return{matcher:e,selector:t=>{let r=">:not([hidden])~:not([hidden])";return t.includes(r)?t:`${t}${r}`}}},pa=[N("@hover",e=>({parent:`${e.parent?`${e.parent} $$ `:""}@media (hover: hover) and (pointer: fine)`,selector:`${e.selector||""}:hover`}))];var da=(e,{theme:t})=>{let r=e.match(/^(.*)\b(placeholder-)(.+)$/);if(r){let[,n="",o,i]=r;if(Re(i,t,"accentColor")||Xl(i))return{matcher:`${n}placeholder-$ ${o}${i}`}}};function Xl(e){let t=e.match(/^op(?:acity)?-?(.+)$/);return t&&t[1]!=null?l.bracket.percent(t[1])!=null:!1}function ma(e){return[da,fa,...Tr(e),...ca,...ua,...la,...sa,...aa,...pa]}function ha(e){if(e==null||e===!1)return[];let t=r=>r.startsWith(":is(")&&r.endsWith(")")?r:r.includes("::")?r.replace(/(.*?)(\s*::.*)/,":is($1)$2"):`:is(${r})`;return[e===!0?r=>{r.entries.forEach(n=>{n[1]!=null&&!String(n[1]).endsWith("!important")&&(n[1]+=" !important")})}:r=>{r.selector.startsWith(e)||(r.selector=`${e} ${t(r.selector)}`)}]}function ga(e){return[...O(Oi(e).postprocess),...ha(e.important)]}var ba=(e={})=>(e.important=e.important??!1,{...un(e),name:"@unocss/preset-wind",theme:ia,rules:ys,shortcuts:vs,variants:ma(e),postprocess:ga(e)});function xa(e,t,r){return`calc(${t} + (${e} - ${t}) * ${r} / 100)`}function ya(e,t,r){let n=[e,t],o=[];for(let s=0;s<2;s++){let a=typeof n[s]=="string"?I(n[s]):n[s];if(!a||!["rgb","rgba"].includes(a.type))return;o.push(a)}let i=[];for(let s=0;s<3;s++)i.push(xa(o[0].components[s],o[1].components[s],r));return{type:"rgb",components:i,alpha:xa(o[0].alpha??1,o[1].alpha??1,r)}}function va(e,t){return ya("#fff",e,t)}function wa(e,t){return ya("#000",e,t)}function Zl(e,t){let r=Number.parseFloat(`${t}`);if(!Number.isNaN(r))return r>0?wa(e,t):va(e,-r)}var Jl={tint:va,shade:wa,shift:Zl};function $a(){let e;return{name:"mix",match(t,r){e||(e=new RegExp(`^mix-(tint|shade|shift)-(-?\\d{1,3})(?:${r.generator.config.separators.join("|")})`));let n=t.match(e);if(n)return{matcher:t.slice(n[0].length),body:o=>(o.forEach(i=>{if(i[1]){let s=I(`${i[1]}`);if(s){let a=Jl[n[1]](s,n[2]);a&&(i[1]=M(a))}}}),o)}}}}var Ql=(e={})=>{let t=ba(e);return{...t,name:"@unocss/preset-uno",variants:[...t.variants,$a()]}},ka=Ql;function eu(e){return e.replace(/-(\w)/g,(t,r)=>r?r.toUpperCase():"")}function Sa(e){return e.charAt(0).toUpperCase()+e.slice(1)}function Ca(e){return e.replace(/(?:^|\B)([A-Z])/g,"-$1").toLowerCase()}var Ra=["Webkit","Moz","ms"];function Ta(e){let t={};function r(n){let o=t[n];if(o)return o;let i=eu(n);if(i!=="filter"&&i in e)return t[n]=Ca(i);i=Sa(i);for(let s=0;s<Ra.length;s++){let a=`${Ra[s]}${i}`;if(a in e)return t[n]=Ca(Sa(a))}return n}return({entries:n})=>n.forEach(o=>{o[0].startsWith("--")||(o[0]=r(o[0]))})}function Ea(e){return e.replace(/&amp;/g,"&").replace(/&gt;/g,">").replace(/&lt;/g,"<")}function Sn(e={}){if(typeof window>"u"){console.warn("@unocss/runtime been used in non-browser environment, skipped.");return}let t=window,r=window.document,n=()=>r.documentElement,o=t.__unocss||{},i=Object.assign({},e,o.runtime),s=i.defaults||{},a=i.cloakAttribute??"un-cloak";i.autoPrefix&&(s.postprocess=O(s.postprocess)).unshift(Ta(r.createElement("div").style)),i.configResolved?.(o,s);let c=Nn(o,s),u=v=>i.inject?i.inject(v):n().prepend(v),p=()=>i.rootElement?i.rootElement():r.body,f=new Map,d=!0,m=new Set,$,C,k=[],z=()=>new Promise(v=>{k.push(v),C!=null&&clearTimeout(C),C=setTimeout(()=>h().then(()=>{let y=k;k=[],y.forEach(R=>R())}),0)});function g(v){if(v.nodeType!==1)return;let y=v;y.hasAttribute(a)&&y.removeAttribute(a),y.querySelectorAll(`[${a}]`).forEach(R=>{R.removeAttribute(a)})}function w(v,y){let R=f.get(v);if(!R)if(R=r.createElement("style"),R.setAttribute("data-unocss-runtime-layer",v),f.set(v,R),y==null)u(R);else{let D=w(y),H=D.parentNode;H?H.insertBefore(R,D.nextSibling):u(R)}return R}async function h(){let v=await c.generate(m);return v.layers.reduce((y,R)=>(w(R,y).innerHTML=v.getLayer(R)??"",R),void 0),m=v.matched,{...v,getStyleElement:y=>f.get(y),getStyleElements:()=>f}}async function b(v){let y=m.size;await c.applyExtractors(v,void 0,m),y!==m.size&&await z()}async function T(v=p()){let y=v&&v.outerHTML;y&&(await b(`${y} ${Ea(y)}`),g(n()),g(v))}let A=new MutationObserver(v=>{d||v.forEach(async y=>{if(y.target.nodeType!==1)return;let R=y.target;for(let D of f)if(R===D[1])return;if(y.type==="childList")y.addedNodes.forEach(async D=>{if(D.nodeType!==1)return;let H=D;$&&!$(H)||(await b(H.outerHTML),g(H))});else{if($&&!$(R))return;if(y.attributeName!==a){let D=Array.from(R.attributes).map(ne=>ne.value?`${ne.name}="${ne.value}"`:ne.name).join(" "),H=`<${R.tagName.toLowerCase()} ${D}>`;await b(H)}R.hasAttribute(a)&&R.removeAttribute(a)}})}),P=!1;function K(){if(P)return;let v=i.observer?.target?i.observer.target():p();v&&(A.observe(v,{childList:!0,subtree:!0,attributes:!0,attributeFilter:i.observer?.attributeFilter}),P=!0)}function re(){i.bypassDefined&&tu(c.blocked),T(),K()}function ge(){r.readyState==="loading"?t.addEventListener("DOMContentLoaded",re):re()}let Qe=t.__unocss_runtime=t.__unocss_runtime={version:c.version,uno:c,async extract(v){V(v)||(v.forEach(y=>m.add(y)),v=""),await b(v)},extractAll:T,inspect(v){$=v},toggleObserver(v){v===void 0?d=!d:d=!!v,!P&&!d&&ge()},update:h,presets:t.__unocss_runtime?.presets??{}};i.ready?.(Qe)!==!1&&(d=!1,ge())}function tu(e=new Set){for(let t=0;t<document.styleSheets.length;t++){let r=document.styleSheets[t],n;try{if(n=r.cssRules||r.rules,!n)continue;Array.from(n).flatMap(o=>o.selectorText?.split(/,/g)||[]).forEach(o=>{o&&(o=o.trim(),o.startsWith(".")&&(o=o.slice(1)),e.add(o))})}catch{continue}}return e}Sn({defaults:{presets:[ka()]}});})();