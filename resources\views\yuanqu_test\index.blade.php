@extends('yuanqu_test.layouts.main')

@section('title', '科技园 - 首页')

@section('styles')
    <link rel="stylesheet" href="{{ asset('static/yuanqu_test/css/index.css') }}" />
@endsection

@section('content')
    <div class="content">
        <section class="section section-1">
            <div class="container grid grid-cols-1 lg:grid-cols-3 gap-8 mx-auto px-4">
                <!-- 轮播图 -->
                <div class="swiper">
                    <div class="swiper-container">
                        <div class="w-full h-full">
                            @if(isset($sliders) && count($sliders) > 0)
                                @foreach($sliders as $slider)
                                    <div class="swiper-slide">
                                        <a href="{{ $slider->url ?? '#' }}" target="_blank">
                                            <img src="{{ asset('storage/' . $slider->image) }}" alt="{{ $slider->title }}" />
                                            <div class="swiper-slide-content">
                                                {{ $slider->title }}
                                            </div>
                                            <div class="date">{{ $slider->created_at->format('Y-m-d') }}</div>
                                        </a>
                                    </div>
                                @endforeach
                            @else
                                <div class="swiper-slide">
                                    <a href="#" target="_blank">
                                        <img src="{{ asset('static/yuanqu_test/images/cover.png') }}" alt="默认轮播图" />
                                        <div class="swiper-slide-content">
                                            欢迎来到科技园
                                        </div>
                                        <div class="date">{{ date('Y-m-d') }}</div>
                                    </a>
                                </div>
                            @endif
                        </div>
                        <div class="swiper-pagination">
                            @if(isset($sliders) && count($sliders) > 0)
                                @foreach($sliders as $index => $slider)
                                    <span class="swiper-pagination-bullet {{ $index == 0 ? 'swiper-pagination-bullet-active' : '' }}"></span>
                                @endforeach
                            @else
                                <span class="swiper-pagination-bullet swiper-pagination-bullet-active"></span>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- 园区动态 -->
                <div class="data-box flex flex-col">
                    <div class="title-wrap flex flex-shrink-0 items-center justify-between gap-2">
                        <div class="title flex items-center gap-4">
                            <h2>园区动态</h2>
                            <span class="sub-title">News</span>
                        </div>
                        <div class="more flex-shrink-0">
                            <a href="{{ url('/news') }}">
                                <img src="{{ asset('static/yuanqu_test/images/more.svg') }}" alt="更多" />
                            </a>
                        </div>
                    </div>
                    <div class="flex-1">
                        <ul class="data-list">
                            @if(isset($news) && count($news) > 0)
                                @foreach($news as $index => $article)
                                    <li class="data-item i{{ $index + 1 }}">
                                        <a href="{{ url('/news/' . $article->id) }}">
                                            <span class="data-title">{{ $article->title }}</span>
                                            <span class="date">{{ $article->created_at->format('Y-m-d') }}</span>
                                        </a>
                                    </li>
                                @endforeach
                            @else
                                <li class="data-item i1">
                                    <span class="data-title">暂无园区动态</span>
                                    <span class="date">{{ date('Y-m-d') }}</span>
                                </li>
                            @endif
                        </ul>
                    </div>
                </div>

                <!-- 通知公告 -->
                <div class="data-box flex flex-col">
                    <div class="title-wrap flex flex-shrink-0 items-center justify-between gap-2">
                        <div class="title flex items-center gap-4">
                            <h2>通知公告</h2>
                            <span class="sub-title">Notice</span>
                        </div>
                        <div class="more flex-shrink-0">
                            <a href="{{ url('/notice') }}">
                                <img src="{{ asset('static/yuanqu_test/images/more.svg') }}" alt="更多" />
                            </a>
                        </div>
                    </div>
                    <div class="flex-1">
                        <ul class="data-list">
                            @if(isset($notices) && count($notices) > 0)
                                @foreach($notices as $index => $article)
                                    <li class="data-item i{{ $index + 1 }}">
                                        <a href="{{ url('/notice/' . $article->id) }}">
                                            <span class="data-title">{{ $article->title }}</span>
                                            <span class="date">{{ $article->created_at->format('Y-m-d') }}</span>
                                        </a>
                                    </li>
                                @endforeach
                            @else
                                <li class="data-item i1">
                                    <span class="data-title">暂无通知公告</span>
                                    <span class="date">{{ date('Y-m-d') }}</span>
                                </li>
                            @endif
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <div class="section section-2">
            <div class="container grid grid-cols-1 lg:grid-cols-2 gap-8 mx-auto px-4">
                <!-- 园区企业 -->
                <div class="w-full flex flex-wrap">
                    <div class="label w-full md:w-1/4">
                        <div class="block-area">
                            <h3 class="block-title">园区企业</h3>
                            <h4 class="block-subtitle">Enterprises</h4>
                            <a class="block-more-btn" href="{{ url('/enterprises') }}">查看全部</a>
                        </div>
                        <div class="enterprise-swiper-pagination swiper-pagination-clickable swiper-pagination-bullets swiper-pagination-horizontal">
                            @if(isset($enterprises) && count($enterprises) > 0)
                                @php $chunks = array_chunk($enterprises->toArray(), 3); @endphp
                                @foreach($chunks as $index => $chunk)
                                    <span class="swiper-pagination-bullet {{ $index == 0 ? 'swiper-pagination-bullet-active' : '' }}" 
                                          tabindex="0" role="button" aria-label="Go to slide {{ $index + 1 }}" 
                                          {{ $index == 0 ? 'aria-current="true"' : '' }}></span>
                                @endforeach
                            @else
                                <span class="swiper-pagination-bullet swiper-pagination-bullet-active" 
                                      tabindex="0" role="button" aria-label="Go to slide 1" aria-current="true"></span>
                            @endif
                        </div>
                    </div>
                    <div class="enterprise-content-wrapper enterprise-content-swiper-container w-full md:w-3/4 swiper-initialized swiper-horizontal swiper-pointer-events swiper-backface-hidden">
                        <div class="enterprise-list-wrapper swiper-wrapper">
                            @if(isset($enterprises) && count($enterprises) > 0)
                                @php $chunks = array_chunk($enterprises->toArray(), 3); @endphp
                                @foreach($chunks as $index => $chunk)
                                    <div class="enterprise-list swiper-slide {{ $index == 0 ? 'swiper-slide-active' : '' }}" 
                                         style="grid-template-columns: repeat(2, 1fr);" role="group" aria-label="{{ $index + 1 }} / {{ count($chunks) }}">
                                        @foreach($chunk as $enterprise)
                                            <a class="enterprise-item" href="{{ url('/enterprises/' . $enterprise['id']) }}">
                                                <img class="logo" src="{{ asset('storage/' . $enterprise['logo']) }}" alt="{{ $enterprise['name'] }}" />
                                                <div class="info">
                                                    <h4 class="name">{{ $enterprise['name'] }}</h4>
                                                    <div class="direction">{{ $enterprise['industry'] ?? '其他行业' }}</div>
                                                </div>
                                            </a>
                                        @endforeach
                                    </div>
                                @endforeach
                            @else
                                <div class="enterprise-list swiper-slide swiper-slide-active" 
                                     style="grid-template-columns: repeat(2, 1fr);" role="group" aria-label="1 / 1">
                                    <div class="enterprise-item">
                                        <div class="info">
                                            <h4 class="name">暂无企业信息</h4>
                                            <div class="direction">敬请期待</div>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>
                        <span class="swiper-notification" aria-live="assertive" aria-atomic="true"></span>
                    </div>
                </div>

                <!-- 科研成果 -->
                <div class="data-box flex flex-col">
                    <div class="title-wrap flex flex-shrink-0 items-center justify-between gap-2">
                        <div class="title flex items-center gap-4">
                            <h2>科研成果</h2>
                            <span class="sub-title">Achievement</span>
                        </div>
                        <div class="more flex-shrink-0">
                            <a href="{{ url('/achievement') }}">
                                <img src="{{ asset('static/yuanqu_test/images/more.svg') }}" alt="更多" />
                            </a>
                        </div>
                    </div>
                    <div class="flex-1">
                        <ul class="data-list">
                            @if(isset($achievements) && count($achievements) > 0)
                                @foreach($achievements as $index => $article)
                                    <li class="data-item i{{ $index + 1 }}">
                                        <a href="{{ url('/achievement/' . $article->id) }}">
                                            <span class="data-title">{{ $article->title }}</span>
                                            <span class="date">{{ $article->created_at->format('Y-m-d') }}</span>
                                        </a>
                                    </li>
                                @endforeach
                            @else
                                <li class="data-item i1">
                                    <span class="data-title">暂无科研成果</span>
                                    <span class="date">{{ date('Y-m-d') }}</span>
                                </li>
                            @endif
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script src="{{ asset('static/yuanqu_test/js/index.js') }}"></script>
@endsection
