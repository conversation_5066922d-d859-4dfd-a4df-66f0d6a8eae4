
<?php $__env->startSection("seo"); ?>
    <?php echo $__env->make("default.layouts.seo",[
        'title' => '创业论坛',
        'keywords' => $category->keywords ?? null,
        'description' => $category->description ?? null,
        ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <link rel="stylesheet" href="<?php echo e(asset('static/'.get_theme().'/css/forum.css'), false); ?>">
<?php $__env->stopSection(); ?>
<?php $__env->startSection("content"); ?>
    <div class="settle_top_search_box forum_search_box">
        <div class="container">
            <div class="current_page">
                <img src="<?php echo e(asset("static/default/images/breadcrumbs-home.png"), false); ?>" alt="" style="width: 16px;height: 16px;margin-right: 8px;">
                <a href="/">首页</a>&nbsp;/&nbsp;创业论坛
            </div>
        </div>
    </div>

    <div class="forum_box">
        <?php if(count($forums)): ?>
            <div class="container">
                <div class="forum_list_box" data-aos="fade-up" data-aos-delay="300">
                    <?php $__currentLoopData = $forums; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <a href="<?php echo e(association_route("front.forum_detail",['id'=>$item->id]), false); ?>">
                            <div class="forum_item">
                                <div class="forum_item_title"><?php echo e($item->title, false); ?></div>
                                <div class="forum_item_data">
                                    <div class="forum_item_data_item">
                                        <span class="forum_item_data_label">主讲人：</span>
                                        <span class="forum_item_data_content"><?php echo e($item->speaker, false); ?></span>
                                    </div>
                                    <div class="forum_item_data_item">
                                        <span class="forum_item_data_label">举办时间：</span>
                                        <span class="forum_item_data_content"><?php echo e($item->time, false); ?></span>
                                    </div>
                                    <div class="forum_item_data_item">
                                        <span class="forum_item_data_label">举办地点：</span>
                                        <span class="forum_item_data_content"><?php echo e($item->address, false); ?></span>
                                    </div>
                                </div>
                            </div>
                        </a>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <div data-aos="fade-up" data-aos-delay="400">
                    <?php echo e($forums->appends($_GET)->links(), false); ?>

                </div>
            </div>
        <?php else: ?>
            <div class="container">
                <div class="empty">
                    <div class="help">暂无内容</div>
                </div>
            </div>
        <?php endif; ?>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection("bottom"); ?>
    <script>

    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make("default.layouts.main", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /mnt/c/Users/<USER>/code/yuanqu/admin/resources/views/default/forum/list.blade.php ENDPATH**/ ?>