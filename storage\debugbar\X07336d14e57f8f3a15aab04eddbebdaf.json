{"__meta": {"id": "X07336d14e57f8f3a15aab04eddbebdaf", "datetime": "2025-06-18 09:50:04", "utime": 1750211404.424503, "method": "GET", "uri": "/dahonglu/forums", "ip": "127.0.0.1"}, "php": {"version": "8.0.26", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750211403.431757, "end": 1750211404.424522, "duration": 0.992764949798584, "duration_str": "993ms", "measures": [{"label": "Booting", "start": 1750211403.431757, "relative_start": 0, "end": 1750211404.049161, "relative_end": 1750211404.049161, "duration": 0.6174039840698242, "duration_str": "617ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1750211404.049171, "relative_start": 0.6174139976501465, "end": 1750211404.424524, "relative_end": 2.1457672119140625e-06, "duration": 0.3753530979156494, "duration_str": "375ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 42567072, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 6, "templates": [{"name": "default.forum.list (resources/views/default/forum/list.blade.php)", "param_count": 1, "params": ["forums"], "type": "blade"}, {"name": "default.layouts.seo (resources/views/default/layouts/seo.blade.php)", "param_count": 7, "params": ["__env", "app", "errors", "forums", "title", "keywords", "description"], "type": "blade"}, {"name": "pagination::bootstrap-4 (resources/views/vendor/pagination/bootstrap-4.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade"}, {"name": "default.layouts.main (resources/views/default/layouts/main.blade.php)", "param_count": 7, "params": ["__env", "app", "errors", "forums", "__currentLoopData", "item", "loop"], "type": "blade"}, {"name": "default.layouts.header (resources/views/default/layouts/header.blade.php)", "param_count": 8, "params": ["__env", "app", "errors", "forums", "__currentLoopData", "item", "loop", "association"], "type": "blade"}, {"name": "default.layouts.footer (resources/views/default/layouts/footer.blade.php)", "param_count": 8, "params": ["__env", "app", "errors", "forums", "__currentLoopData", "item", "loop", "association"], "type": "blade"}]}, "route": {"uri": "GET {association}/forums", "middleware": "web, front_association", "controller": "App\\Http\\Controllers\\Home\\IndexController@forum", "namespace": "App\\Http\\Controllers", "prefix": "/{association}", "where": [], "as": "front.forums", "file": "<a href=\"phpstorm://open?file=/mnt/c/Users/<USER>/code/yuanqu/admin/app/Http/Controllers/Home/IndexController.php&line=283\">app/Http/Controllers/Home/IndexController.php:283-295</a>"}, "queries": {"nb_statements": 11, "nb_failed_statements": 0, "accumulated_duration": 0.014339999999999999, "accumulated_duration_str": "14.34ms", "statements": [{"sql": "select * from `associations` where `alias` = 'dahonglu' and `associations`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON>"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "middleware", "name": "front_association", "line": 68}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "line": 50}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "line": 167}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "line": 78}], "duration": 0.0008, "duration_str": "800μs", "stmt_id": "middleware::front_association:68", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 0, "width_percent": 5.579}, {"sql": "select count(*) as aggregate from `forums` where `show` = 1 and `status` = 'passed' and (exists (select * from `associations` where `forums`.`association_id` = `associations`.`id` and `show` = 1 and `associations`.`deleted_at` is null) or `association_id` = 0) and `association_id` = 2", "type": "query", "params": [], "bindings": ["1", "passed", "1", "0", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/app/Http/Controllers/Home/IndexController.php", "line": 293}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 48}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721}], "duration": 0.00108, "duration_str": "1.08ms", "stmt_id": "/app/Http/Controllers/Home/IndexController.php:293", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 5.579, "width_percent": 7.531}, {"sql": "select * from `forums` where `show` = 1 and `status` = 'passed' and (exists (select * from `associations` where `forums`.`association_id` = `associations`.`id` and `show` = 1 and `associations`.`deleted_at` is null) or `association_id` = 0) and `association_id` = 2 order by `top` desc, `order` asc, `created_at` desc, `id` desc limit 15 offset 0", "type": "query", "params": [], "bindings": ["1", "passed", "1", "0", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/app/Http/Controllers/Home/IndexController.php", "line": 293}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 48}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Router.php", "line": 721}], "duration": 0.00087, "duration_str": "870μs", "stmt_id": "/app/Http/Controllers/Home/IndexController.php:293", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 13.11, "width_percent": 6.067}, {"sql": "select * from `navs` where (`parent_id` = 0 or `parent_id` is null) and `show` = 1 and `association_id` = 2 and `navs`.`deleted_at` is null order by `order` asc", "type": "query", "params": [], "bindings": ["0", "1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "4b551dc7569e49db71ac2552133a3f418724512c", "line": 25}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 19, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.0007199999999999999, "duration_str": "720μs", "stmt_id": "view::4b551dc7569e49db71ac2552133a3f418724512c:25", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 19.177, "width_percent": 5.021}, {"sql": "select * from `navs` where `parent_id` = 47 and `show` = 1 and `association_id` = 2 and `navs`.`deleted_at` is null order by `order` asc", "type": "query", "params": [], "bindings": ["47", "1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "4b551dc7569e49db71ac2552133a3f418724512c", "line": 31}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 19, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.0006, "duration_str": "600μs", "stmt_id": "view::4b551dc7569e49db71ac2552133a3f418724512c:31", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 24.198, "width_percent": 4.184}, {"sql": "select * from `navs` where `parent_id` = 14 and `show` = 1 and `association_id` = 2 and `navs`.`deleted_at` is null order by `order` asc", "type": "query", "params": [], "bindings": ["14", "1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "4b551dc7569e49db71ac2552133a3f418724512c", "line": 31}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 19, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.00057, "duration_str": "570μs", "stmt_id": "view::4b551dc7569e49db71ac2552133a3f418724512c:31", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 28.382, "width_percent": 3.975}, {"sql": "select * from `navs` where `parent_id` = 12 and `show` = 1 and `association_id` = 2 and `navs`.`deleted_at` is null order by `order` asc", "type": "query", "params": [], "bindings": ["12", "1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "4b551dc7569e49db71ac2552133a3f418724512c", "line": 31}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 19, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.00075, "duration_str": "750μs", "stmt_id": "view::4b551dc7569e49db71ac2552133a3f418724512c:31", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 32.357, "width_percent": 5.23}, {"sql": "select * from `navs` where `parent_id` = 13 and `show` = 1 and `association_id` = 2 and `navs`.`deleted_at` is null order by `order` asc", "type": "query", "params": [], "bindings": ["13", "1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "4b551dc7569e49db71ac2552133a3f418724512c", "line": 31}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 19, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.0011799999999999998, "duration_str": "1.18ms", "stmt_id": "view::4b551dc7569e49db71ac2552133a3f418724512c:31", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 37.587, "width_percent": 8.229}, {"sql": "select * from `navs` where `parent_id` = 45 and `show` = 1 and `association_id` = 2 and `navs`.`deleted_at` is null order by `order` asc", "type": "query", "params": [], "bindings": ["45", "1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "4b551dc7569e49db71ac2552133a3f418724512c", "line": 31}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 19, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.0053, "duration_str": "5.3ms", "stmt_id": "view::4b551dc7569e49db71ac2552133a3f418724512c:31", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 45.816, "width_percent": 36.96}, {"sql": "select * from `navs` where `parent_id` = 46 and `show` = 1 and `association_id` = 2 and `navs`.`deleted_at` is null order by `order` asc", "type": "query", "params": [], "bindings": ["46", "1", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "4b551dc7569e49db71ac2552133a3f418724512c", "line": 31}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 19, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.00083, "duration_str": "830μs", "stmt_id": "view::4b551dc7569e49db71ac2552133a3f418724512c:31", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 82.775, "width_percent": 5.788}, {"sql": "select * from `pages` where `alias` = '园区简介' limit 1", "type": "query", "params": [], "bindings": ["园区简介"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "1ab61f277331ad0356d6d880ff0067a8dad67ade", "line": 32}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 61}, {"index": 20, "namespace": null, "name": "/vendor/facade/ignition/src/Views/Engines/CompilerEngine.php", "line": 37}], "duration": 0.00164, "duration_str": "1.64ms", "stmt_id": "view::1ab61f277331ad0356d6d880ff0067a8dad67ade:32", "connection": "<PERSON><PERSON><PERSON><PERSON>huiyu", "start_percent": 88.563, "width_percent": 11.437}]}, "models": {"data": {"App\\Models\\Page": 1, "App\\Models\\Nav": 9, "App\\Models\\Forum": 3, "App\\Models\\Association": 1}, "count": 14}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "W1QQUVUZnGkhSaNrQUvEkYJaufhZnFzbmGwEe37y", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/dahonglu/forums\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/dahonglu/forums", "status_code": "<pre class=sf-dump id=sf-dump-1123932054 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1123932054\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2057329432 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2057329432\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1931303419 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1931303419\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-978512745 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/dahonglu</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">zh-CN,zh-TW;q=0.9,zh;q=0.8,en-US;q=0.7,en;q=0.6,ja;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1194 characters\">53f831c002c9c7a136212a92ba8080fe=ecd2bd9a-1783-48b6-8855-af9b5aee3b08.r-2gkkZDMy9ZdqaaJO8YxqsVzAw; order=id%20desc; serverType=nginx; sites_path=/www/wwwroot; site_model=php; bt_user_info=%7B%22status%22%3Atrue%2C%22msg%22%3A%22%u83B7%u53D6%u6210%u529F%21%22%2C%22data%22%3A%7B%22username%22%3A%22187****1129%22%7D%7D; pro_end=-1; ltd_end=-1; 6078d1b0c3c9622461c6aedbe995157a=4f29ffe2-08db-4f02-924b-7a38270964b8.VH-JjNjSIsFvC53sp3CgNW1rFmY; db_page_model=mysql; backup_path=/www/backup; XSRF-TOKEN=eyJpdiI6IkhrajZKazlCbEhJaXZ0bVkrMWJFZHc9PSIsInZhbHVlIjoialNZM21FMXlENjZTWUhYR3ZsWVpvMzZCYWllQnM0MWVDWjVOdEZoL280M0dxU2huZEFJOE1tQk0vL09NclZYU3RwbkpzU01BdHNIVXI1MEtRQmFiblNQanR6OVdjNU1vK2lzRHNnOFVGRE1URHdFdzRvTXRyVTZtOVNGaERKdnEiLCJtYWMiOiIwZGE3ZGM5YzQ1ZmI4NjM2NjYxODNjNGExNjBhNDFiYjc3MmY1Y2RiZWNjZWJkM2IxZjEyYWQ5MWE4NzJhMDJiIiwidGFnIjoiIn0%3D; _session=eyJpdiI6IjhWeHVvYVhoNEZpdUJaQ3lBTitqYXc9PSIsInZhbHVlIjoianlRYTZ0V3dXTGVSU1YzTHVQWDhnR1BSdVNDN0xZN2pjUzRHcmNTVldFUWc2S0piYXRJQkFIVFVGN2RUZUk0VG1ZdlBQdWp1NWYyckpWZ2hkemJMaTcwZmYwdkFRQU9lY3pZbURiZVRLWTlzRmVJMkd6M3VuMnZaSmJ6RmkwdmgiLCJtYWMiOiI5NTI2N2NjMTQ1OTEzYjlhMjRjZjViYjRkY2ZhN2U5YmRlOTNlODQ2N2NiNDQ0ZjVlMmQ3NzkwOTU5ZWM1NGU4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-978512745\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-958295823 data-indent-pad=\"  \"><span class=sf-dump-note>array:33</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"43 characters\">/mnt/c/Users/<USER>/code/yuanqu/admin/public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">59154</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.0.26 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"16 characters\">/dahonglu/forums</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"53 characters\">/mnt/c/Users/<USER>/code/yuanqu/admin/public/index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"16 characters\">/dahonglu/forums</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"26 characters\">/index.php/dahonglu/forums</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_PRAGMA</span>\" => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/dahonglu</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"56 characters\">zh-CN,zh-TW;q=0.9,zh;q=0.8,en-US;q=0.7,en;q=0.6,ja;q=0.5</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1194 characters\">53f831c002c9c7a136212a92ba8080fe=ecd2bd9a-1783-48b6-8855-af9b5aee3b08.r-2gkkZDMy9ZdqaaJO8YxqsVzAw; order=id%20desc; serverType=nginx; sites_path=/www/wwwroot; site_model=php; bt_user_info=%7B%22status%22%3Atrue%2C%22msg%22%3A%22%u83B7%u53D6%u6210%u529F%21%22%2C%22data%22%3A%7B%22username%22%3A%22187****1129%22%7D%7D; pro_end=-1; ltd_end=-1; 6078d1b0c3c9622461c6aedbe995157a=4f29ffe2-08db-4f02-924b-7a38270964b8.VH-JjNjSIsFvC53sp3CgNW1rFmY; db_page_model=mysql; backup_path=/www/backup; XSRF-TOKEN=eyJpdiI6IkhrajZKazlCbEhJaXZ0bVkrMWJFZHc9PSIsInZhbHVlIjoialNZM21FMXlENjZTWUhYR3ZsWVpvMzZCYWllQnM0MWVDWjVOdEZoL280M0dxU2huZEFJOE1tQk0vL09NclZYU3RwbkpzU01BdHNIVXI1MEtRQmFiblNQanR6OVdjNU1vK2lzRHNnOFVGRE1URHdFdzRvTXRyVTZtOVNGaERKdnEiLCJtYWMiOiIwZGE3ZGM5YzQ1ZmI4NjM2NjYxODNjNGExNjBhNDFiYjc3MmY1Y2RiZWNjZWJkM2IxZjEyYWQ5MWE4NzJhMDJiIiwidGFnIjoiIn0%3D; _session=eyJpdiI6IjhWeHVvYVhoNEZpdUJaQ3lBTitqYXc9PSIsInZhbHVlIjoianlRYTZ0V3dXTGVSU1YzTHVQWDhnR1BSdVNDN0xZN2pjUzRHcmNTVldFUWc2S0piYXRJQkFIVFVGN2RUZUk0VG1ZdlBQdWp1NWYyckpWZ2hkemJMaTcwZmYwdkFRQU9lY3pZbURiZVRLWTlzRmVJMkd6M3VuMnZaSmJ6RmkwdmgiLCJtYWMiOiI5NTI2N2NjMTQ1OTEzYjlhMjRjZjViYjRkY2ZhN2U5YmRlOTNlODQ2N2NiNDQ0ZjVlMmQ3NzkwOTU5ZWM1NGU4IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1750211403.4318</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1750211403</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-958295823\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1708127828 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>53f831c002c9c7a136212a92ba8080fe</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>serverType</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sites_path</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>site_model</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>bt_user_info</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>pro_end</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>ltd_end</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>6078d1b0c3c9622461c6aedbe995157a</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>db_page_model</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>backup_path</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">W1QQUVUZnGkhSaNrQUvEkYJaufhZnFzbmGwEe37y</span>\"\n  \"<span class=sf-dump-key>_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4ksm6s1cHLimZMhS5l1qQCq2aW4KXQ34hjmsPQDP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1708127828\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-790081241 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 18 Jun 2025 01:50:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InM4djlqRElXMjhhVlphRndZK0ViV2c9PSIsInZhbHVlIjoiUnNaMXNpVDk4dWh0VitvK1N4STBGZVYxcHp2WTZZVmpQWTJ0UmtIYzJEb1ZTUEVuNjBzQlI3aG02Nm5lTDJUNXZidWVwWlVCZ3UyYndoanpYeWozQjJaaU9HRUtPY1VjMUpZRWZNOXBYbkdQTUVuNmhqcWU3TTJvV1BqY2xKSlYiLCJtYWMiOiJkNWQ0OWE4MGI4NGVjZjBiZmJiZWQ2MzMzY2ZkNWFjZmZjZGQyOGYzNzZiZjJkMDIwMTVlMzZhMjQwMWMzZTA4IiwidGFnIjoiIn0%3D; expires=Wed, 18-Jun-2025 03:50:04 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"436 characters\">_session=eyJpdiI6ImJnajY2K0xscVBMUnM5MllLaXB6VEE9PSIsInZhbHVlIjoiRjRGYkQ0WThCK0t5UmNqbnJ5VzRKcnU4ZkhpMGc4Zngxc1BnOTVlTmRLYytIUVNOb0lrV3RHN3dMaTVPK281WFNzazNlSFhWQjhwbXRrTzF4Um9VbVBtbWRXMHc3Z09mK3hUV2srSExub3Z6VnBVM0RIcTlMMlpBdkxVQVZuMlEiLCJtYWMiOiIyNzY4MDM0NDk0MWE1NThlYzhkMjRhZmQ4ZmNmYzI3YTA1NjU4Yjk1MzBlNjNmMDlhNjYzYTg0ZTVhNjkyYmM2IiwidGFnIjoiIn0%3D; expires=Wed, 18-Jun-2025 03:50:04 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InM4djlqRElXMjhhVlphRndZK0ViV2c9PSIsInZhbHVlIjoiUnNaMXNpVDk4dWh0VitvK1N4STBGZVYxcHp2WTZZVmpQWTJ0UmtIYzJEb1ZTUEVuNjBzQlI3aG02Nm5lTDJUNXZidWVwWlVCZ3UyYndoanpYeWozQjJaaU9HRUtPY1VjMUpZRWZNOXBYbkdQTUVuNmhqcWU3TTJvV1BqY2xKSlYiLCJtYWMiOiJkNWQ0OWE4MGI4NGVjZjBiZmJiZWQ2MzMzY2ZkNWFjZmZjZGQyOGYzNzZiZjJkMDIwMTVlMzZhMjQwMWMzZTA4IiwidGFnIjoiIn0%3D; expires=Wed, 18-Jun-2025 03:50:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"408 characters\">_session=eyJpdiI6ImJnajY2K0xscVBMUnM5MllLaXB6VEE9PSIsInZhbHVlIjoiRjRGYkQ0WThCK0t5UmNqbnJ5VzRKcnU4ZkhpMGc4Zngxc1BnOTVlTmRLYytIUVNOb0lrV3RHN3dMaTVPK281WFNzazNlSFhWQjhwbXRrTzF4Um9VbVBtbWRXMHc3Z09mK3hUV2srSExub3Z6VnBVM0RIcTlMMlpBdkxVQVZuMlEiLCJtYWMiOiIyNzY4MDM0NDk0MWE1NThlYzhkMjRhZmQ4ZmNmYzI3YTA1NjU4Yjk1MzBlNjNmMDlhNjYzYTg0ZTVhNjkyYmM2IiwidGFnIjoiIn0%3D; expires=Wed, 18-Jun-2025 03:50:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-790081241\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-675383169 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">W1QQUVUZnGkhSaNrQUvEkYJaufhZnFzbmGwEe37y</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/dahonglu/forums</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-675383169\", {\"maxDepth\":0})</script>\n"}}