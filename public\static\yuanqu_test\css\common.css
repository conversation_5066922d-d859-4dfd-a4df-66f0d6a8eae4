/* reset */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
body {
  font-family: Microsoft YaHei, Microsoft YaHei, Arial, sans-serif;
  font-size: 16px;
  line-height: 1.5;
  color: #000000;
  margin: 0;
  padding: 0;
}

ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

ul li {
  display: inline-block;
}

a {
  color: unset;
  text-decoration: none;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
}

img,
a {
  display: inline-block;
}

input,
textarea {
  outline: none;
  border: none;
}

.search {
  display: flex;
  align-items: center;
  gap: 10px;
}

.search input {
  width: 367px;
  height: 40px;
  background: #ffffff;
  border-radius: 31px 31px 31px 31px;
  opacity: 0.27;
  padding: 0 28px 0 38px;
  border: 1px solid #a2a1a1;
}

.search button {
  width: 100px;
  height: 40px;
  background-color: #ffffff;
  border: 1px solid #4d4b4b;
  opacity: 0.27;
  border-radius: 31px 31px 31px 31px;
}

/* .nav ul {
  display: flex;
  align-items: center;
  list-style: none;
}

.nav ul li {
  margin-right: 20px;
}

.nav ul li a {
  text-decoration: none;
  color: #fff;
}

.nav ul li a:hover,
.nav ul li a:active {
  color: #000;
} */

header {
  height: 256px;
  position: relative;
}

header .logo {
  padding-top: 80px;
}

header nav {
  position: relative;
  top: 10px;
}
header nav .nav-list li {
  /* border-right: 1px solid rgba(255, 255, 255, 0.5); */
  line-height: 17px;
  padding: 30px;
  position: relative;
  overflow: visible;
}

header nav .nav-list li::after {
  content: "";
  display: block;
  width: 1px;
  height: 17px;
  background: rgba(255, 255, 255, 0.5);
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}
header nav .nav-list li:last-child::after {
  width: 0;
}

header nav .nav-list li a {
  width: 100%;
  text-align: center;
  /* color: rgba(255, 255, 255, 0.89); */
}

header nav .nav-list li.selected a,
header nav .nav-list li a:hover {
  /* color: #fff; */
  font-weight: bold;
}
header nav .nav-list li .sub-menu {
  position: absolute;
  top: 70px;
  left: 50%;
  transform: translateX(-50%);
  width: 150px;
  background: #fff;
  box-shadow: 0px 3px 6px 1px rgba(0, 0, 0, 0.16);
  border-top: 3px solid #0068B7;
  padding-top: 3px;
  flex-direction: column;
  display: none;
  z-index: 1;
}
header nav .nav-list li:hover .sub-menu {
  display: flex;
}
header nav .nav-list li .sub-menu li {
  border-right: none;
  padding: 0;
  width: 100%;
  height: 46px;
  text-align: center;
}
header nav .nav-list li .sub-menu li a {
  color: #000;
  font-size: 14px;
  padding: 10px 0;
}
header nav .nav-list li .sub-menu li:hover {
  background: #f6f6f6;
}
header nav .nav-list li .sub-menu li:hover a {
  color: #0068B7;
}

.link {
  background: #ffffff;
  padding: 15px 0;
}

.link .container {
  display: flex;
  align-items: center;
}

.link h2 {
  font-size: 14px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.3);
}

.link ul {
  display: flex;
  align-items: center;
  list-style: none;
  gap: 50px;
}

.link ul li a {
  text-decoration: none;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.6);
}

.link ul li:hover,
.link ul li:active {
  color: #0068b7;
}

.footer {
  width: 100%;
  height: 194px;
  background-image: url(../images/footer.png);
  background-size: auto 100%;
  color: #fff;
}

.footer .footer-top {
  display: flex;
  padding: 52px 0;
}

.footer .footer-top .footer-top-left {
  width: 550px;
}

.footer .footer-top .footer-top-left img {
  width: 295px;
  height: 41px;
}

.footer .footer-top .footer-top-right {
}

.footer .footer-top .footer-top-right ul {
  display: flex;
  gap: 120px;
}

.footer .footer-top .footer-top-right ul li {
  display: flex;
  flex-direction: column;
}

.footer .footer-top .footer-top-right ul li .footer-top-right-title {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
}

.footer .footer-top .footer-top-right ul li .footer-top-right-content {
  font-size: 16px;
}

.footer .copyright {
  font-size: 12px;
  text-align: center;
  color: rgba(255, 255, 255, 0.5);
}

.container {
  width: 100%;
  max-width: 1600px;
  margin: 0 auto;
}

body > .content {
  min-height: calc(100vh - 450px);
  padding-bottom: 20px;
}

.data-list {
}
.data-list .data-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 32px;
  padding: 20px 0;
  line-height: 1.1;
  border-bottom: 1px dashed #dddddd;
  overflow: hidden;
}

.data-list .data-item:hover {
  transition: transform 0.3s;
  transform: translateX(10px);
}

.data-list .data-item .data-title {
  flex: 1;
  font-size: 16px;
  color: #000000;
  text-align: left;
  overflow: hidden;
  display: flex;
  align-items: center;
}
.data-list .data-item .data-title a {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.data-list .data-item .date {
  flex-shrink: 0;
  font-size: 14px;
  color: #bcbec0;
}
.data-list .data-item:hover .data-title {
  color: #0068b7;
}
.data-list .data-item .tag {
  margin-left: 12px;
}
.tag.pending {
  color: #007bff;
}
.tag.done {
  color: #ff3300;
}

.banner {
  height: 158px;
}
.banner img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.left-box {
  position: relative;
}
.left-box h3 {
  color: #ffffff;
  font-size: 22px;
  font-weight: bold;
  padding: 14px 20px;
  background: #0068b7;
}
.left-box h3::before {
  content: "";
  display: inline-block;
  width: 3px;
  height: 22px;
  margin-right: 20px;
  transform: translateY(4px);
  background: #ffffff;
}
.left-box .left-list {
  padding-left: 42px;
  border-left: 8px solid #0068b7;
}
.left-box .left-list li {
  border-bottom: 1px solid #dcdcdc;
}
.left-box .left-list li:hover,
.left-box .left-list li.selected {
  border-bottom: 1px solid #0068b7;
}
.left-box .left-list li a {
  width: 100%;
  padding: 15px 0;
  color: #333333;
  font-size: 18px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.left-box .left-list li a .icon {
  display: none;
  width: 5px;
  height: 8px;
}
.left-box .left-list li:hover a,
.left-box .left-list li.selected a {
  color: #0068b7;
}

.left-box .left-list li:hover a .icon,
.left-box .left-list li.selected a .icon {
  display: inline-block;
}

.breadcrumb {
  font-size: 14px;
  color: #b4b4b4;
  padding: 18px 0;
  border-bottom: 1px solid #dcdcdc;
}
.breadcrumb .label {
  margin-right: 12px;
}
.breadcrumb .possplit {
  position: relative;
  margin: 0 6px;
}
.breadcrumb .possplit::before {
  content: "";
  display: inline-block;
  width: 1px;
  height: 12px;
  background: #e6e6e6;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
}
.breadcrumb a:hover {
  color: #0068b7;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  padding: 20px 0;
}
.pagination a {
  min-width: 35px;
  height: 35px;
  line-height: 1;
  text-align: center;
  padding: 8px;
  font-size: 15px;
  border: 1px solid #dcdcdc;
  color: rgba(0, 0, 0, 0.9);
}
.pagination a:hover {
  color: #0068b7;
}
.pagination .ellipsis {
  border: none;
}
.pagination .prev,
.pagination .next {
  padding: 8px 13px;
}

.article {
  padding: 36px 0;
}
.article h1 {
  text-align: center;
  color: #111111;
  font-size: 26px;
  margin-bottom: 22px;
}
.article .article-info {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.4);
}
.article .article-info .info-item .icon {
  width: 16px;
  height: 16px;
}
.article .article-content {
  margin-top: 40px;
}
.article .article-content p {
  font-size: 16px;
  color: #333333;
  line-height: 31px;
  margin-bottom: 28px;
}
.article .article-attachments {
  margin-top: 40px;
  background: #f9f9f9;
}
.article .article-attachments ul li {
  padding: 10px 20px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #dcdcdc;
}
.article .article-attachments ul li:last-child {
  border-bottom: none;
}
.article .article-attachments ul li img {
  width: 15px;
  height: 13px;
  margin-right: 16px;
}
.article .article-attachments ul li a {
  color: #111111;
  font-size: 16px;
}
.article .article-attachments ul li a:hover {
  color: #0068b7;
}

.article .page-nav {
  margin-top: 60px;
}
.article .page-nav a {
  width: 110px;
  height: 42px;
  line-height: 42px;
  border-radius: 20px 20px 20px 20px;
  border: 1px solid #e6e6e6;
  color: #999999;
  font-size: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}
.article .page-nav a:hover {
  color: #0068b7;
  border-color: #0068b7;
}

.empty {
  text-align: center;
  padding: 140px 0;
}
.empty img {
  width: 176px;
  height: 150px;
  margin: 20px auto;
}
.empty p {
  text-align: center;
  font-size: 14px;
  color: #9195a3;
}
