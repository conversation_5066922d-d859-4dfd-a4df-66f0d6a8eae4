@extends('yuanqu_test.layouts.main')

@section('title', '{{ $article->title ?? "文章详情" }} - 科技园')

@section('meta')
    @if(isset($article))
        <meta name="keywords" content="{{ $article->keywords }}" />
        <meta name="description" content="{{ $article->description ?? \Illuminate\Support\Str::limit(strip_tags($article->content), 200) }}" />
    @endif
@endsection

@section('styles')
    <link rel="stylesheet" type="text/css" href="{{ asset('static/yuanqu_test/css/detail.css') }}" />
@endsection

@section('content')
    <div class="content">
        <div class="banner">
            <img src="{{ asset('static/yuanqu_test/images/banner.png') }}" alt="banner" />
        </div>
        <div class="box">
            <div class="container grid grid-cols-4 lg:gap-25">
                <!-- 左侧栏 -->
                <div class="left-col col-span-4 lg:col-span-1">
                    <div class="left-box">
                        <h3>
                            <span>{{ $category->name ?? '栏目导航' }}</span>
                        </h3>
                        <div>
                            @if(isset($sidebar_categories) && count($sidebar_categories) > 0)
                                <ul class="left-list flex flex-col">
                                    @foreach($sidebar_categories as $sidebar_category)
                                        <li class="{{ isset($category) && $sidebar_category->id == $category->id ? 'active' : '' }}">
                                            <a href="{{ url('/' . $sidebar_category->alias) }}">
                                                {{ $sidebar_category->name }}
                                                <img src="{{ asset('static/yuanqu_test/images/arrow-right.svg') }}" class="icon" />
                                            </a>
                                        </li>
                                    @endforeach
                                </ul>
                            @else
                                <ul class="left-list flex flex-col">
                                    <li class="active">
                                        <a href="#">
                                            {{ $category->name ?? '当前栏目' }}
                                            <img src="{{ asset('static/yuanqu_test/images/arrow-right.svg') }}" class="icon" />
                                        </a>
                                    </li>
                                </ul>
                            @endif
                        </div>
                    </div>
                </div>
                
                <!-- 主内容区 -->
                <div class="main-col col-span-4 lg:col-span-3">
                    <div class="breadcrumb">
                        <div>
                            <span class="label">您的位置：</span>
                            <a href="{{ url('/') }}">首页</a> / 
                            @if(isset($category))
                                <a href="{{ url('/' . $category->alias) }}">{{ $category->name }}</a> / 
                            @endif
                            文章详情
                        </div>
                    </div>
                    
                    <div class="main-box">
                        @if(isset($article))
                            <div class="article">
                                <div>
                                    <h1>{{ $article->title }}</h1>
                                    <div class="article-info flex justify-center items-center gap-20">
                                        <span class="info-item flex items-center gap-2">
                                            <img src="{{ asset('static/yuanqu_test/images/time.svg') }}" alt="time" class="icon" />
                                            发布于 {{ $article->created_at->format('Y-m-d') }}
                                        </span>
                                        <span class="info-item flex items-center gap-2">
                                            <img src="{{ asset('static/yuanqu_test/images/eye.svg') }}" alt="view" class="icon" />
                                            浏览量：{{ $article->view ?? 0 }}
                                        </span>
                                    </div>
                                    <div class="article-content">
                                        {!! $article->content !!}
                                    </div>
                                    
                                    @if(isset($article->extra) && $article->getExtra() && isset($article->getExtra()->attachments))
                                        <div class="article-attachments">
                                            <ul class="attachments-list flex flex-col">
                                                @foreach($article->getExtra()->attachments as $attachment)
                                                    <li>
                                                        <img src="{{ asset('static/yuanqu_test/images/attachment.svg') }}" alt="attachment" />
                                                        <a href="{{ asset('storage/' . $attachment->url) }}" target="_blank">{{ $attachment->name }}</a>
                                                    </li>
                                                @endforeach
                                            </ul>
                                        </div>
                                    @endif
                                </div>

                                @if(isset($prev_article) || isset($next_article))
                                    <div class="page-nav flex justify-between items-center">
                                        @if(isset($prev_article))
                                            <a href="{{ url('/' . ($category->alias ?? 'article') . '/' . $prev_article->id) }}" class="prev">
                                                <img src="{{ asset('static/yuanqu_test/images/page-arrow-left.svg') }}" class="icon" />
                                                上一篇：{{ \Illuminate\Support\Str::limit($prev_article->title, 30) }}
                                            </a>
                                        @else
                                            <span class="prev disabled">没有上一篇了</span>
                                        @endif
                                        
                                        @if(isset($next_article))
                                            <a href="{{ url('/' . ($category->alias ?? 'article') . '/' . $next_article->id) }}" class="next">
                                                下一篇：{{ \Illuminate\Support\Str::limit($next_article->title, 30) }}
                                                <img src="{{ asset('static/yuanqu_test/images/page-arrow-right.svg') }}" class="icon" />
                                            </a>
                                        @else
                                            <span class="next disabled">没有下一篇了</span>
                                        @endif
                                    </div>
                                @endif
                            </div>
                        @else
                            <div class="empty">
                                <img src="{{ asset('static/yuanqu_test/images/empty.png') }}" alt="empty" />
                                <p>文章不存在或已被删除</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        $(function () {
            // 如果文章内容为空，显示空状态
            if (!$(".article").length) {
                $(".empty").removeClass("hidden");
            }
            
            // 增加浏览量
            @if(isset($article))
                $.ajax({
                    url: '{{ url("/api/article/" . $article->id . "/view") }}',
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        // 浏览量增加成功
                    },
                    error: function() {
                        // 忽略错误
                    }
                });
            @endif
        });
    </script>
@endsection
